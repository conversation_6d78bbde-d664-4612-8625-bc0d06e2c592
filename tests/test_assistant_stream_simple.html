<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assistant API Streaming Test</title>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #4285F4;
            --secondary-color: #34A853;
            --background-color: #f9f9f9;
            --card-background: #ffffff;
            --text-color: #333333;
            --border-color: #e0e0e0;
        }

        body {
            font-family: 'Roboto', sans-serif;
            background-color: var(--background-color);
            color: var(--text-color);
            max-width: 900px;
            margin: 0 auto;
            padding: 30px;
            line-height: 1.6;
        }

        .container {
            background-color: var(--card-background);
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 25px;
            margin-bottom: 20px;
        }

        h1 {
            color: var(--primary-color);
            text-align: center;
            margin-bottom: 30px;
            font-weight: 500;
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }

        textarea, input[type="text"] {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-family: 'Roboto', sans-serif;
            font-size: 14px;
            transition: border-color 0.3s;
            box-sizing: border-box;
        }

        textarea {
            height: 120px;
            resize: vertical;
        }

        textarea:focus, input[type="text"]:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
        }

        .button-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        button {
            padding: 10px 20px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 500;
            transition: background-color 0.3s, transform 0.1s;
            flex: 1;
        }

        button:hover {
            background-color: #3367D6;
        }

        button:active {
            transform: scale(0.98);
        }

        #clear {
            background-color: #757575;
        }

        #clear:hover {
            background-color: #616161;
        }

        .response-container {
            background-color: var(--card-background);
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }

        #response {
            white-space: pre-wrap;
            min-height: 200px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            padding: 15px;
            background-color: #fafafa;
            font-size: 15px;
            line-height: 1.6;
        }

        #metadata {
            margin-top: 15px;
            font-size: 12px;
            color: #757575;
            padding: 8px;
            background-color: #f5f5f5;
            border-radius: 4px;
        }

        .reference-section, .questions-section {
            margin-top: 20px;
            border-top: 1px solid var(--border-color);
            padding-top: 15px;
        }

        .reference-section h3, .questions-section h3 {
            color: var(--primary-color);
            font-size: 16px;
            margin-bottom: 10px;
        }

        .reference-item {
            margin-bottom: 15px;
            padding: 10px;
            background-color: #f9f9f9;
            border-radius: 4px;
            border-left: 3px solid var(--primary-color);
        }

        .reference-title {
            font-weight: 500;
            color: var(--primary-color);
            margin-bottom: 5px;
            font-size: 14px;
        }

        .reference-snippet {
            font-size: 12px;
            color: #555;
            margin-bottom: 5px;
        }

        .reference-url {
            font-size: 11px;
            color: #888;
            word-break: break-all;
        }

        .reference-url a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .reference-url a:hover {
            text-decoration: underline;
        }

        .follow-up-question {
            display: block;
            margin-bottom: 8px;
            padding: 8px 12px;
            background-color: #f0f7ff;
            border-radius: 4px;
            color: var(--primary-color);
            cursor: pointer;
            transition: background-color 0.2s, transform 0.1s;
            font-size: 13px;
            border-left: 3px solid var(--primary-color);
        }

        .follow-up-question:hover {
            background-color: #e0f0ff;
            transform: translateX(2px);
        }

        .loading-container {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .loading-spinner {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid rgba(66, 133, 244, 0.3);
            border-radius: 50%;
            border-top-color: var(--primary-color);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .typing-indicator {
            display: inline-block;
            position: relative;
            min-width: 30px;
        }

        .typing-indicator span {
            display: inline-block;
            width: 8px;
            height: 8px;
            background-color: var(--primary-color);
            border-radius: 50%;
            margin: 0 2px;
            opacity: 0.6;
            animation: typing 1.4s infinite both;
        }

        .typing-indicator span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-indicator span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0% { transform: translateY(0); }
            50% { transform: translateY(-5px); }
            100% { transform: translateY(0); }
        }

        .metadata-section {
            margin-top: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .metadata-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .metadata-title {
            font-weight: 500;
            color: #555;
            font-size: 14px;
        }

        .metadata-badge {
            display: inline-block;
            padding: 3px 8px;
            background-color: var(--primary-color);
            color: white;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
        }

        /* Financial Dashboard Styles */
        #financial-dashboard {
            display: none;
            background-color: #1e1e1e;
            color: #ffffff;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            font-family: 'Roboto', sans-serif;
        }

        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 1px solid #333;
            padding-bottom: 10px;
        }

        .dashboard-header h2 {
            margin: 0;
            font-size: 24px;
            font-weight: 500;
        }

        .dashboard-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 400;
            color: #aaa;
        }

        .dashboard-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #333;
        }

        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            font-weight: 500;
        }

        .tab.active {
            border-bottom: 3px solid #4CAF50;
            color: #4CAF50;
        }

        .financial-metrics {
            margin-bottom: 30px;
        }

        .metric-label {
            font-size: 16px;
            color: #aaa;
            margin-bottom: 5px;
        }

        .metric-value {
            font-size: 36px;
            font-weight: 500;
            margin-bottom: 15px;
        }

        .growth-metrics {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .growth-metric {
            background-color: #333;
            padding: 10px 15px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .positive {
            color: #4CAF50;
        }

        .negative {
            color: #F44336;
        }

        .chart-container {
            height: 300px;
            margin-bottom: 30px;
            position: relative;
        }

        .chart-y-axis {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 50px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            color: #aaa;
            font-size: 12px;
            padding: 10px 0;
        }

        .chart-bars {
            margin-left: 50px;
            height: 100%;
            display: flex;
            align-items: flex-end;
            gap: 20px;
        }

        .chart-bar {
            flex: 1;
            background-color: #4CAF50;
            position: relative;
            border-radius: 4px 4px 0 0;
            min-width: 30px;
        }

        .chart-bar-label {
            position: absolute;
            bottom: -25px;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 12px;
            color: #aaa;
        }

        .chart-bar-value {
            position: absolute;
            top: -25px;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 12px;
            color: #fff;
        }

        .collapsible-sections {
            margin-top: 30px;
        }

        .section {
            margin-bottom: 15px;
            border: 1px solid #333;
            border-radius: 6px;
            overflow: hidden;
        }

        .section-header {
            padding: 15px;
            background-color: #333;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-header:after {
            content: '▼';
            font-size: 12px;
            transition: transform 0.3s;
        }

        .section-header.collapsed:after {
            transform: rotate(-90deg);
        }

        .section-content {
            padding: 15px;
            display: none;
        }

        .financial-table {
            width: 100%;
            border-collapse: collapse;
        }

        .financial-table th, .financial-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #333;
        }

        .financial-table th {
            color: #aaa;
            font-weight: normal;
        }
    </style>
</head>
<body>
    <h1>GoGPT Test</h1>
    
    <div class="container">
        <div class="form-group">
            <label for="query">Query:</label>
            <textarea id="query">Analyse Tesla 2024 earning report</textarea>
        </div>
        
        <div class="form-group">
            <label for="system">System:</label>
            <textarea id="system"></textarea>
        </div>
        
        <div class="form-group">
            <label for="thread_id">Thread ID (optional):</label>
            <input type="text" id="thread_id">
        </div>
        
        <div class="form-group">
            <label for="model">Model (optional):</label>
            <input type="text" id="model" value="openai/gpt-4-turbo">
        </div>
        
        <div class="button-group">
            <button id="send">Send Request</button>
            <button id="clear">Clear Response</button>
        </div>
    </div>
    
    <div class="loading-container" id="loading">
        <div class="loading-spinner"></div>
        <p>Connecting to assistant...</p>
    </div>
    
    <div class="response-container">
        <div id="response"></div>
        <div id="metadata"></div>
        
        <div class="metadata-section" style="display: none;" id="metadata-container">
            <div class="metadata-header">
                <div class="metadata-title">Response Metadata</div>
                <div class="metadata-badge">AI Assistant</div>
            </div>
            
            <div id="references" class="reference-section" style="display: none;">
                <h3>References</h3>
                <div id="reference-list"></div>
            </div>
            
            <div id="follow-up" class="questions-section" style="display: none;">
                <h3>Follow-up Questions</h3>
                <div id="question-list"></div>
            </div>
        </div>
        
        <!-- Financial Dashboard -->
        <div id="financial-dashboard">
            <div class="dashboard-header">
                <h2 id="company-ticker">MSFT</h2>
                <h3>Financial Statements</h3>
            </div>
            
            <div class="dashboard-tabs">
                <div class="tab active" data-tab="annual">Annual</div>
                <div class="tab" data-tab="quarterly">Quarterly</div>
            </div>
            
            <div class="financial-metrics">
                <div class="metric-label">Revenue</div>
                <div class="metric-value" id="revenue-value">245.1B USD</div>
                
                <div class="growth-metrics">
                    <div class="growth-metric">
                        <span>3Y Avg. Growth:</span>
                        <span id="growth-3y" class="positive">+11.2%</span>
                    </div>
                    <div class="growth-metric">
                        <span>5Y Avg. Growth:</span>
                        <span id="growth-5y" class="positive">+14.4%</span>
                    </div>
                </div>
                
                <div id="revenue-chart" class="chart-container"></div>
            </div>
            
            <div class="collapsible-sections">
                <div class="section">
                    <div class="section-header">Income Statement</div>
                    <div class="section-content" id="income-statement-content">
                        <table class="financial-table">
                            <thead>
                                <tr>
                                    <th>Metric</th>
                                    <th>Value</th>
                                    <th>Year</th>
                                    <th>Period</th>
                                </tr>
                            </thead>
                            <tbody id="income-statement-table">
                                <!-- Will be populated dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div class="section">
                    <div class="section-header">Balance Sheet</div>
                    <div class="section-content" id="balance-sheet-content">
                        <p>Balance sheet data not available in this view.</p>
                    </div>
                </div>
                
                <div class="section">
                    <div class="section-header">Cash Flow Statement</div>
                    <div class="section-content" id="cash-flow-content">
                        <p>Cash flow data not available in this view.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.getElementById('send').addEventListener('click', () => {
            const query = document.getElementById('query').value;
            const system = document.getElementById('system').value;
            const threadId = document.getElementById('thread_id').value;
            const model = document.getElementById('model').value;
            const responseDiv = document.getElementById('response');
            const metadataDiv = document.getElementById('metadata');
            const loadingDiv = document.getElementById('loading');
            
            // Show loading animation
            loadingDiv.style.display = 'block';
            
            // Clear previous response
            responseDiv.innerHTML = '<div class="typing-indicator"><span></span><span></span><span></span></div>';
            metadataDiv.textContent = '';
            
            // Prepare request payload
            const payload = {
                query: query + system,
                stream: true
            };
            
            // Add optional parameters if provided
            if (threadId) {
                payload.thread_id = threadId;
            }
            
            if (model) {
                payload.model = model;
            }
            
            // Define the API endpoint
            const apiUrl = '/api/gpt/assistant';
            // const apiUrl = 'https://app.dev.addxgo.io/api/gpt/pub/assistant';
            
            // Use fetch with POST method
            fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'text/event-stream',
                    "Auth0-Sub": "user1"
                },
                body: JSON.stringify(payload)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                
                // Get a reader from the response body stream
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                let buffer = '';
                let fullContent = '';
                let isFirstMessage = true;
                
                // Process the stream
                function processStream() {
                    return reader.read().then(({ done, value }) => {
                        if (done) {
                            console.log('Stream complete');
                            return;
                        }
                        
                        // Decode the chunk and add it to our buffer
                        buffer += decoder.decode(value, { stream: true });
                        
                        // Process any complete SSE messages in the buffer
                        const lines = buffer.split('\n\n');
                        buffer = lines.pop(); // Keep the last (potentially incomplete) part
                        
                        // Process each complete message
                        lines.forEach(line => {
                            if (line.startsWith('data: ')) {
                                const jsonStr = line.substring(6); // Remove 'data: ' prefix
                                try {
                                    const data = JSON.parse(jsonStr);
                                    console.log('Received data:', data);
                                    
                                    // Hide loading animation once we start receiving data
                                    loadingDiv.style.display = 'none';
                                    
                                    // Update metadata
                                    if (data.session_id || data.round_id) {
                                        metadataDiv.textContent = `Session ID: ${data.session_id || 'None'}, Round ID: ${data.round_id || 'None'}`;
                                        if (data.thread_id) {
                                            document.getElementById('thread_id').value = data.thread_id;
                                            metadataDiv.textContent += `, Thread ID: ${data.thread_id}`;
                                        }
                                    }
                                    
                                    // Handle metadata message type
                                    if (data.type === 'metadata') {
                                        console.log('Received metadata:', data);
                                        
                                        // Show the metadata container
                                        document.getElementById('metadata-container').style.display = 'block';
                                        
                                        // Handle reference URLs
                                        const referencesDiv = document.getElementById('references');
                                        const referenceListDiv = document.getElementById('reference-list');
                                        referenceListDiv.innerHTML = '';
                                        
                                        if (data.reference_urls && data.reference_urls.length > 0) {
                                            data.reference_urls.forEach(ref => {
                                                const refItem = document.createElement('div');
                                                refItem.className = 'reference-item';
                                                
                                                // Handle both old format (string) and new format (object)
                                                if (typeof ref === 'string') {
                                                    refItem.innerHTML = `
                                                        <div class="reference-url"><a href="${ref}" target="_blank">${ref}</a></div>
                                                    `;
                                                } else {
                                                    const title = ref.title || 'Reference';
                                                    const snippet = ref.snippet || '';
                                                    const url = ref.url || '#';
                                                    
                                                    refItem.innerHTML = `
                                                        <div class="reference-title">${title}</div>
                                                        ${snippet ? `<div class="reference-snippet">${snippet}</div>` : ''}
                                                        <div class="reference-url"><a href="${url}" target="_blank">${url}</a></div>
                                                    `;
                                                }
                                                
                                                referenceListDiv.appendChild(refItem);
                                            });
                                            
                                            referencesDiv.style.display = 'block';
                                        } else {
                                            referencesDiv.style.display = 'none';
                                        }
                                        
                                        // Handle follow-up questions
                                        const followUpDiv = document.getElementById('follow-up');
                                        const questionListDiv = document.getElementById('question-list');
                                        questionListDiv.innerHTML = '';
                                        
                                        if (data.follow_up_questions && data.follow_up_questions.length > 0) {
                                            data.follow_up_questions.forEach(question => {
                                                const questionItem = document.createElement('div');
                                                questionItem.className = 'follow-up-question';
                                                questionItem.textContent = question;
                                                
                                                // Add click handler to populate the query field
                                                questionItem.addEventListener('click', () => {
                                                    document.getElementById('query').value = question;
                                                    // Optional: Auto-scroll to the query input
                                                    document.getElementById('query').scrollIntoView({ behavior: 'smooth' });
                                                });
                                                
                                                questionListDiv.appendChild(questionItem);
                                            });
                                            
                                            followUpDiv.style.display = 'block';
                                        } else {
                                            followUpDiv.style.display = 'none';
                                        }
                                    }
                                    // Handle company_facts data type
                                    else if (data.type === 'company_facts') {
                                        console.log('Received company_facts:', data);
                                        
                                        // Process and display financial data
                                        processCompanyFacts(data.data);
                                        
                                        // Show the financial dashboard
                                        document.getElementById('financial-dashboard').style.display = 'block';
                                    }
                                    // Handle content
                                    else if (data.content === '[DONE]') {
                                        console.log('Stream complete');
                                        reader.cancel();
                                    } else if (data.content) {
                                        console.log('Received content:', data.content);
                                        
                                        // Skip "Assistant is thinking..." message
                                        if (isFirstMessage && data.content === "Assistant is thinking...") {
                                            isFirstMessage = false;
                                            return;
                                        }
                                        
                                        // If this is the first real message, clear the typing indicator
                                        if (isFirstMessage) {
                                            responseDiv.textContent = '';
                                            isFirstMessage = false;
                                        }
                                        
                                        fullContent += data.content;
                                        responseDiv.textContent = fullContent;
                                    }
                                } catch (e) {
                                    console.error('Error parsing message:', e, jsonStr);
                                    responseDiv.textContent += `\nError parsing message: ${e.message}\n`;
                                    loadingDiv.style.display = 'none';
                                }
                            }
                        });
                        
                        // Continue processing the stream
                        return processStream();
                    });
                }
                
                // Start processing the stream
                return processStream();
            })
            .catch(error => {
                console.error('Fetch error:', error);
                responseDiv.textContent = `Connection error: ${error.message}. Please try again.`;
                loadingDiv.style.display = 'none';
            });
        });
        
        document.getElementById('clear').addEventListener('click', () => {
            document.getElementById('response').textContent = '';
            document.getElementById('metadata').textContent = '';
            document.getElementById('loading').style.display = 'none';
            document.getElementById('metadata-container').style.display = 'none';
            document.getElementById('references').style.display = 'none';
            document.getElementById('reference-list').innerHTML = '';
            document.getElementById('follow-up').style.display = 'none';
            document.getElementById('question-list').innerHTML = '';
            document.getElementById('financial-dashboard').style.display = 'none';
        });
        
        // Financial Dashboard Functions
        function processCompanyFacts(data) {
            // Set company ticker (using MSFT as default for now)
            document.getElementById('company-ticker').textContent = 'MSFT';
            
            // Extract revenue data
            const revenueData = data.Revenues || [];
            const netIncomeData = data.NetIncomeLoss || [];
            const grossProfitData = data.GrossProfit || [];
            const operatingIncomeData = data.OperatingIncomeLoss || [];
            
            // Sort by fiscal year (descending)
            const sortedRevenueData = [...revenueData].sort((a, b) => {
                if (b.fiscal_year !== a.fiscal_year) {
                    return b.fiscal_year - a.fiscal_year;
                }
                // If same year, sort by period (FY comes first, then Q4, Q3, Q2, Q1)
                const periodOrder = { 'FY': 0, 'Q4': 1, 'Q3': 2, 'Q2': 3, 'Q1': 4 };
                return periodOrder[a.fiscal_period] - periodOrder[b.fiscal_period];
            });
            
            // Separate annual and quarterly data
            const annualData = sortedRevenueData.filter(item => item.fiscal_period === "FY");
            const quarterlyData = sortedRevenueData.filter(item => item.fiscal_period.startsWith("Q"));
            
            // Display the most recent annual revenue
            if (annualData.length > 0) {
                const latestRevenue = annualData[0];
                displayRevenue(latestRevenue);
                
                // Calculate and display growth metrics
                calculateGrowthMetrics(annualData);
                
                // Generate chart data
                generateRevenueChart(annualData);
                
                // Populate income statement
                populateIncomeStatement(data);
            }
            
            // Set up tab switching
            setupTabs();
            
            // Set up collapsible sections
            setupCollapsibleSections();
        }
        
        function displayRevenue(revenueData) {
            const valueElement = document.getElementById('revenue-value');
            
            // Format the value (convert to billions and add USD)
            const valueInBillions = parseFloat(revenueData.value) / 1000000000;
            valueElement.textContent = `${valueInBillions.toFixed(1)}B USD`;
        }
        
        function calculateGrowthMetrics(annualData) {
            // Need at least 3 years of data for 3Y growth and 5 years for 5Y growth
            if (annualData.length >= 3) {
                const growth3y = calculateAverageGrowth(annualData.slice(0, 3));
                const growth3yElement = document.getElementById('growth-3y');
                
                growth3yElement.textContent = `${growth3y > 0 ? '+' : ''}${(growth3y * 100).toFixed(1)}%`;
                growth3yElement.className = growth3y >= 0 ? 'positive' : 'negative';
            }
            
            if (annualData.length >= 5) {
                const growth5y = calculateAverageGrowth(annualData.slice(0, 5));
                const growth5yElement = document.getElementById('growth-5y');
                
                growth5yElement.textContent = `${growth5y > 0 ? '+' : ''}${(growth5y * 100).toFixed(1)}%`;
                growth5yElement.className = growth5y >= 0 ? 'positive' : 'negative';
            }
        }
        
        function calculateAverageGrowth(data) {
            // Calculate compound annual growth rate (CAGR)
            const oldestValue = parseFloat(data[data.length - 1].value);
            const newestValue = parseFloat(data[0].value);
            const years = data.length - 1;
            
            if (oldestValue <= 0 || years <= 0) return 0;
            
            const cagr = Math.pow(newestValue / oldestValue, 1 / years) - 1;
            return cagr;
        }
        
        function generateRevenueChart(data) {
            const chartContainer = document.getElementById('revenue-chart');
            chartContainer.innerHTML = ''; // Clear previous chart
            
            // Create chart structure
            const chartYAxis = document.createElement('div');
            chartYAxis.className = 'chart-y-axis';
            
            const chartBars = document.createElement('div');
            chartBars.className = 'chart-bars';
            
            // Get max value for scaling
            const maxValue = Math.max(...data.map(item => parseFloat(item.value)));
            
            // Create Y-axis labels
            for (let i = 5; i >= 0; i--) {
                const label = document.createElement('div');
                const value = (maxValue * i / 5) / 1000000000;
                label.textContent = `${value.toFixed(1)}B`;
                chartYAxis.appendChild(label);
            }
            
            // Create bars (limit to 5 most recent years)
            const displayData = data.slice(0, 5).reverse(); // Reverse to show oldest to newest
            
            displayData.forEach(item => {
                const bar = document.createElement('div');
                bar.className = 'chart-bar';
                
                const value = parseFloat(item.value);
                const height = (value / maxValue * 100) + '%';
                bar.style.height = height;
                
                // Add year label
                const yearLabel = document.createElement('div');
                yearLabel.className = 'chart-bar-label';
                yearLabel.textContent = item.fiscal_year;
                bar.appendChild(yearLabel);
                
                // Add value label
                const valueLabel = document.createElement('div');
                valueLabel.className = 'chart-bar-value';
                const valueInBillions = value / 1000000000;
                valueLabel.textContent = `${valueInBillions.toFixed(1)}B`;
                bar.appendChild(valueLabel);
                
                chartBars.appendChild(bar);
            });
            
            chartContainer.appendChild(chartYAxis);
            chartContainer.appendChild(chartBars);
        }
        
        function populateIncomeStatement(data) {
            const tableBody = document.getElementById('income-statement-table');
            tableBody.innerHTML = ''; // Clear previous content
            
            // Add revenue data
            if (data.Revenues) {
                addFinancialRows(tableBody, data.Revenues, 'Revenue');
            }
            
            // Add gross profit data
            if (data.GrossProfit) {
                addFinancialRows(tableBody, data.GrossProfit, 'Gross Profit');
            }
            
            // Add operating income data
            if (data.OperatingIncomeLoss) {
                addFinancialRows(tableBody, data.OperatingIncomeLoss, 'Operating Income');
            }
            
            // Add net income data
            if (data.NetIncomeLoss) {
                addFinancialRows(tableBody, data.NetIncomeLoss, 'Net Income');
            }
        }
        
        function addFinancialRows(tableBody, data, metricName) {
            // Sort by fiscal year (descending) and then by period
            const sortedData = [...data].sort((a, b) => {
                if (b.fiscal_year !== a.fiscal_year) {
                    return b.fiscal_year - a.fiscal_year;
                }
                // If same year, sort by period (FY comes first, then Q4, Q3, Q2, Q1)
                const periodOrder = { 'FY': 0, 'Q4': 1, 'Q3': 2, 'Q2': 3, 'Q1': 4 };
                return periodOrder[a.fiscal_period] - periodOrder[b.fiscal_period];
            });
            
            // Add rows for each data point
            sortedData.forEach(item => {
                const row = document.createElement('tr');
                
                // Format value in billions
                const valueInBillions = parseFloat(item.value) / 1000000000;
                const formattedValue = `$${valueInBillions.toFixed(2)}B`;
                
                row.innerHTML = `
                    <td>${metricName}</td>
                    <td>${formattedValue}</td>
                    <td>${item.fiscal_year}</td>
                    <td>${item.fiscal_period}</td>
                `;
                
                tableBody.appendChild(row);
            });
        }
        
        function setupTabs() {
            document.querySelectorAll('.dashboard-tabs .tab').forEach(tab => {
                tab.addEventListener('click', () => {
                    // Remove active class from all tabs
                    document.querySelectorAll('.dashboard-tabs .tab').forEach(t => {
                        t.classList.remove('active');
                    });
                    
                    // Add active class to clicked tab
                    tab.classList.add('active');
                    
                    // Switch data view based on selected tab
                    const tabType = tab.getAttribute('data-tab');
                    // This would be implemented to switch between annual and quarterly data
                    // For now, we'll just log it
                    console.log(`Switched to ${tabType} view`);
                });
            });
        }
        
        function setupCollapsibleSections() {
            document.querySelectorAll('.section-header').forEach(header => {
                // Show the first section by default
                if (header === document.querySelectorAll('.section-header')[0]) {
                    header.nextElementSibling.style.display = 'block';
                } else {
                    header.classList.add('collapsed');
                }
                
                header.addEventListener('click', () => {
                    const content = header.nextElementSibling;
                    const isCollapsed = header.classList.contains('collapsed');
                    
                    if (isCollapsed) {
                        content.style.display = 'block';
                        header.classList.remove('collapsed');
                    } else {
                        content.style.display = 'none';
                        header.classList.add('collapsed');
                    }
                });
            });
        }
    </script>
</body>
</html>
