# OpenAPI Specification Automation

This document describes the automated tools for managing the OpenAPI specification in the Go GPT Backend project.

## Overview

The project includes automated tools to help maintain the OpenAPI specification (`gpt.openapi.yaml`) by:
- Scanning the codebase for API endpoints
- Comparing discovered endpoints with the existing specification
- Validating YAML syntax
- Generating reports on missing or extra endpoints

## Available Commands

### `make openapi-validate`
Validates the YAML syntax of the OpenAPI specification file.

```bash
make openapi-validate
```

**Output:**
- ✅ Success: "OpenAPI YAML syntax is valid"
- ❌ Error: YAML syntax errors with details

### `make openapi-update`
Analyzes the codebase and compares it with the existing OpenAPI specification.

```bash
make openapi-update
```

**Features:**
- Scans all API route files (FastAPI and Flask)
- Compares discovered endpoints with existing specification
- Generates a detailed analysis report
- Saves report to `openapi_analysis_report.txt`

**Output includes:**
- Number of endpoints found in codebase vs. specification
- List of missing endpoints (in code but not in spec)
- List of extra endpoints (in spec but not found in code)
- Recommendations for updates

### `make openapi-check`
Same as `openapi-update` but exits with error code if discrepancies are found.

```bash
make openapi-check
```

**Use cases:**
- CI/CD pipeline validation
- Pre-commit hooks
- Automated testing

**Exit codes:**
- 0: OpenAPI specification is up to date
- 1: Discrepancies found

### `make openapi-generate`
Generates a new OpenAPI specification from scratch by scanning the codebase.

```bash
make openapi-generate
```

**Features:**
- Scans all API route files
- Generates basic OpenAPI structure
- Merges with existing specification to preserve manual additions
- Creates a complete OpenAPI 3.0 specification

## File Structure

```
scripts/
├── generate_openapi.py    # Full OpenAPI generation
└── update_openapi.py      # Analysis and comparison tool
```

## Supported Route Types

The automation tools can detect endpoints from:

### FastAPI Routes
```python
@router.get("/endpoint")
@router.post("/endpoint")
@app.get("/endpoint")
```

### Flask Routes
```python
@api.route("/endpoint", methods=["GET"])
@blueprint.route("/endpoint", methods=["POST", "PUT"])
```

## Detected Files

The tools automatically scan:
- `api/async_*.py` - FastAPI async route files
- `api/*.py` - Flask route files (excluding async files)
- `app.py` - Main Flask application
- `async_app.py` - Main FastAPI application

## Report Format

The analysis report includes:

```
============================================================
OpenAPI Specification Analysis Report
============================================================
Generated: 2025-06-05 14:17:32

📊 Summary:
  • Endpoints in codebase: 54
  • Endpoints in OpenAPI spec: 50
  • Missing from spec: 4
  • Extra in spec: 0

❌ Missing from OpenAPI specification:
  • GET /new-endpoint
  • POST /another-endpoint

✅ OpenAPI specification is up to date!
============================================================
```

## Integration with CI/CD

### GitHub Actions Example
```yaml
name: OpenAPI Validation
on: [push, pull_request]

jobs:
  validate-openapi:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.9'
      - name: Install dependencies
        run: pip install -r requirements.txt
      - name: Validate OpenAPI
        run: make openapi-check
```

### Pre-commit Hook
```bash
#!/bin/sh
# .git/hooks/pre-commit
make openapi-check
if [ $? -ne 0 ]; then
    echo "❌ OpenAPI specification is out of date. Run 'make openapi-update' to see details."
    exit 1
fi
```

## Best Practices

1. **Regular Updates**: Run `make openapi-update` after adding new endpoints
2. **Validation**: Always run `make openapi-validate` before committing changes
3. **Review Reports**: Check the generated reports for accuracy
4. **Manual Additions**: The tools preserve manually added schemas and descriptions
5. **Version Control**: Commit both the OpenAPI file and analysis reports

## Troubleshooting

### Common Issues

**"YAML syntax error"**
- Check for indentation issues in `gpt.openapi.yaml`
- Ensure proper YAML formatting
- Use `make openapi-validate` to identify specific errors

**"Missing endpoints detected"**
- Review the analysis report
- Add missing endpoints to the OpenAPI specification
- Consider if endpoints are internal/deprecated

**"Extra endpoints in spec"**
- Check if endpoints were removed from code
- Verify endpoint paths and methods
- Update or remove outdated specifications

### Manual Fixes

If automatic detection fails:
1. Check the route file syntax
2. Ensure decorators follow standard patterns
3. Add endpoints manually to the OpenAPI specification
4. Update the automation scripts if needed

## Contributing

When adding new endpoints:
1. Follow standard FastAPI/Flask patterns
2. Run `make openapi-update` to check for missing endpoints
3. Update the OpenAPI specification manually if needed
4. Validate with `make openapi-validate`
5. Include both code and specification changes in your PR

## Future Enhancements

Planned improvements:
- Automatic schema generation from Pydantic models
- Parameter detection from function signatures
- Response schema inference
- Integration with OpenAPI generators
- Support for additional frameworks
