stages:
- release

variables:
  OUTPUT_NAME: __bin__/$CI_PROJECT_NAME
  CONTAINER_IMAGE: $CI_REGISTRY_IMAGE:latest
  REGISTRY_LOCATION: asia-southeast1

.dev_rules:
  rules:
  - if: '$CI_COMMIT_BRANCH == "main"'

.test_rules:
  rules:
  - if: '$CI_COMMIT_BRANCH == "test"'

.prod_rules:
  rules:
  - if: '$CI_COMMIT_BRANCH == "prod"'

auth_gcp:
  image: google/cloud-sdk:443.0.0-alpine
  stage: release
  variables:
    WORKLOAD_IDENTITY_PROJECT_NUMBER: ************
    WORKLOAD_IDENTITY_POOL: identity-pool-for-gitlab-ci-deve
    WORKLOAD_IDENTITY_PROVIDER: oidc-for-gitlab-ci-deve
    SERVICE_ACCOUNT: <EMAIL>
    GOOGLE_APPLICATION_CREDENTIALS: $CI_BUILDS_DIR/.workload_identity.wlconfig
  id_tokens:
    WORKLOAD_IDENTITY_TOKEN:
      aud: https://iam.googleapis.com/projects/$WORKLOAD_IDENTITY_PROJECT_NUMBER/locations/global/workloadIdentityPools/$WORKLOAD_IDENTITY_POOL/providers/$WORKLOAD_IDENTITY_PROVIDER
  script:
  - |-
    echo $WORKLOAD_IDENTITY_TOKEN > $CI_BUILDS_DIR/.workload_identity.jwt
    cat << EOF > $GOOGLE_APPLICATION_CREDENTIALS
    {
      "type": "external_account",
      "audience": "//iam.googleapis.com/projects/$WORKLOAD_IDENTITY_PROJECT_NUMBER/locations/global/workloadIdentityPools/$WORKLOAD_IDENTITY_POOL/providers/$WORKLOAD_IDENTITY_PROVIDER",
      "subject_token_type": "urn:ietf:params:oauth:token-type:jwt",
      "token_url": "https://sts.googleapis.com/v1/token",
      "credential_source": {
        "file": "$CI_BUILDS_DIR/.workload_identity.jwt"
      },
      "service_account_impersonation_url": "https://iamcredentials.googleapis.com/v1/projects/-/serviceAccounts/$SERVICE_ACCOUNT:generateAccessToken"
    }
    EOF
  - gcloud auth login --cred-file=$GOOGLE_APPLICATION_CREDENTIALS
  # This is not secure as the credentials can be downloaded via build artifacts, better to use vault to pass env to another job
  - echo "ID_TOKEN=$(gcloud auth print-access-token)" >> release.env
  after_script:
  - echo "Deploying Docs to bucket..."
  - gsutil cp -r ./gpt.openapi.yaml gs://swagger.dev.addxgo.io/
  - echo "Docs successfully deployed."
  artifacts:
    reports:
      dotenv: release.env
  rules:
  - !reference [ .dev_rules, rules ]

release_gcp:
  image: docker:24.0.4
  stage: release
  variables:
    CONTAINER_RELEASE_IMAGE: $REGISTRY_LOCATION-docker.pkg.dev/go-wallet-dev-396512/go-gpt-backend-service/$CONTAINER_IMAGE
  services:
  - docker:24.0.4-dind
  script:
  - docker build --pull --no-cache --tag $CONTAINER_IMAGE .
  - docker tag $CONTAINER_IMAGE $CONTAINER_RELEASE_IMAGE
  - echo $ID_TOKEN | docker login -u oauth2accesstoken --password-stdin https://$REGISTRY_LOCATION-docker.pkg.dev
  - docker push $CONTAINER_RELEASE_IMAGE
  needs:
  - auth_gcp
  rules:
  - !reference [ .dev_rules, rules ]

auth_gcp_test:
  image: google/cloud-sdk:443.0.0-alpine
  stage: release
  variables:
    WORKLOAD_IDENTITY_PROJECT_NUMBER: ************
    WORKLOAD_IDENTITY_POOL: identity-pool-for-gitlab-ci-test
    WORKLOAD_IDENTITY_PROVIDER: oidc-for-gitlab-ci-test
    SERVICE_ACCOUNT: <EMAIL>
    GOOGLE_APPLICATION_CREDENTIALS: $CI_BUILDS_DIR/.workload_identity.wlconfig
  id_tokens:
    WORKLOAD_IDENTITY_TOKEN:
      aud: https://iam.googleapis.com/projects/$WORKLOAD_IDENTITY_PROJECT_NUMBER/locations/global/workloadIdentityPools/$WORKLOAD_IDENTITY_POOL/providers/$WORKLOAD_IDENTITY_PROVIDER
  script:
  - |-
    echo $WORKLOAD_IDENTITY_TOKEN > $CI_BUILDS_DIR/.workload_identity.jwt
    cat << EOF > $GOOGLE_APPLICATION_CREDENTIALS
    {
      "type": "external_account",
      "audience": "//iam.googleapis.com/projects/$WORKLOAD_IDENTITY_PROJECT_NUMBER/locations/global/workloadIdentityPools/$WORKLOAD_IDENTITY_POOL/providers/$WORKLOAD_IDENTITY_PROVIDER",
      "subject_token_type": "urn:ietf:params:oauth:token-type:jwt",
      "token_url": "https://sts.googleapis.com/v1/token",
      "credential_source": {
        "file": "$CI_BUILDS_DIR/.workload_identity.jwt"
      },
      "service_account_impersonation_url": "https://iamcredentials.googleapis.com/v1/projects/-/serviceAccounts/$SERVICE_ACCOUNT:generateAccessToken"
    }
    EOF
  - gcloud auth login --cred-file=$GOOGLE_APPLICATION_CREDENTIALS
  # This is not secure as the credentials can be downloaded via build artifacts, better to use vault to pass env to another job
  - echo "ID_TOKEN=$(gcloud auth print-access-token)" >> release.env
  after_script:
  - gcloud auth list
  artifacts:
    reports:
      dotenv: release.env
  rules:
  - !reference [ .test_rules, rules ]

release_gcp_test:
  image: docker:24.0.4
  stage: release
  variables:
    CONTAINER_RELEASE_IMAGE: $REGISTRY_LOCATION-docker.pkg.dev/go-wallet-test-396515/go-gpt-backend-service/$CONTAINER_IMAGE
  services:
  - docker:24.0.4-dind
  script:
  - docker build --pull --no-cache --tag $CONTAINER_IMAGE .
  - docker tag $CONTAINER_IMAGE $CONTAINER_RELEASE_IMAGE
  - echo $ID_TOKEN | docker login -u oauth2accesstoken --password-stdin https://$REGISTRY_LOCATION-docker.pkg.dev
  - docker push $CONTAINER_RELEASE_IMAGE
  needs:
  - auth_gcp_test
  rules:
  - !reference [ .test_rules, rules ]

auth_gcp_prod:
  image: google/cloud-sdk:443.0.0-alpine
  stage: release
  variables:
    WORKLOAD_IDENTITY_PROJECT_NUMBER: ************
    WORKLOAD_IDENTITY_POOL: identity-pool-for-gitlab-ci-prod
    WORKLOAD_IDENTITY_PROVIDER: oidc-for-gitlab-ci-prod
    SERVICE_ACCOUNT: <EMAIL>
    GOOGLE_APPLICATION_CREDENTIALS: $CI_BUILDS_DIR/.workload_identity.wlconfig
  id_tokens:
    WORKLOAD_IDENTITY_TOKEN:
      aud: https://iam.googleapis.com/projects/$WORKLOAD_IDENTITY_PROJECT_NUMBER/locations/global/workloadIdentityPools/$WORKLOAD_IDENTITY_POOL/providers/$WORKLOAD_IDENTITY_PROVIDER
  script:
  - |-
    echo $WORKLOAD_IDENTITY_TOKEN > $CI_BUILDS_DIR/.workload_identity.jwt
    cat << EOF > $GOOGLE_APPLICATION_CREDENTIALS
    {
      "type": "external_account",
      "audience": "//iam.googleapis.com/projects/$WORKLOAD_IDENTITY_PROJECT_NUMBER/locations/global/workloadIdentityPools/$WORKLOAD_IDENTITY_POOL/providers/$WORKLOAD_IDENTITY_PROVIDER",
      "subject_token_type": "urn:ietf:params:oauth:token-type:jwt",
      "token_url": "https://sts.googleapis.com/v1/token",
      "credential_source": {
        "file": "$CI_BUILDS_DIR/.workload_identity.jwt"
      },
      "service_account_impersonation_url": "https://iamcredentials.googleapis.com/v1/projects/-/serviceAccounts/$SERVICE_ACCOUNT:generateAccessToken"
    }
    EOF
  - gcloud auth login --cred-file=$GOOGLE_APPLICATION_CREDENTIALS
  # This is not secure as the credentials can be downloaded via build artifacts, better to use vault to pass env to another job
  - echo "ID_TOKEN=$(gcloud auth print-access-token)" >> release.env
  artifacts:
    reports:
      dotenv: release.env
  rules:
  - !reference [ .prod_rules, rules ]

release_gcp_prod:
  image: docker:24.0.4
  stage: release
  variables:
    CONTAINER_RELEASE_IMAGE: $REGISTRY_LOCATION-docker.pkg.dev/go-wallet-production-396515/go-gpt-backend-service/$CONTAINER_IMAGE
  services:
  - docker:24.0.4-dind
  script:
  - docker build --pull --no-cache --tag $CONTAINER_IMAGE .
  - docker tag $CONTAINER_IMAGE $CONTAINER_RELEASE_IMAGE
  - echo $ID_TOKEN | docker login -u oauth2accesstoken --password-stdin https://$REGISTRY_LOCATION-docker.pkg.dev
  - docker push $CONTAINER_RELEASE_IMAGE
  needs:
  - auth_gcp_prod
  rules:
  - !reference [ .prod_rules, rules ]
