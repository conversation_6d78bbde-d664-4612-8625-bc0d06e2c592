.PHONY: setup run test lint clean openapi-check openapi-update openapi-generate openapi-validate openapi-all

setup:
	pip install -r requirements.txt

run:
	python app.py

test:
	pytest tests/

lint:
	flake8 .

clean:
	find . -type d -name __pycache__ -exec rm -r {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type f -name "*.pyd" -delete
	find . -type f -name ".coverage" -delete
	find . -type d -name "*.egg-info" -exec rm -r {} +
	find . -type d -name "*.egg" -exec rm -r {} +
	find . -type d -name ".pytest_cache" -exec rm -r {} +
	find . -type d -name ".coverage" -exec rm -r {} +
	find . -type d -name "htmlcov" -exec rm -r {} +
	find . -type d -name "dist" -exec rm -r {} +
	find . -type d -name "build" -exec rm -r {} +

# OpenAPI Specification Management
openapi-check:
	@echo "🔍 Checking OpenAPI specification..."
	python scripts/update_openapi.py --check

openapi-update:
	@echo "📝 Analyzing OpenAPI specification..."
	python scripts/update_openapi.py

openapi-generate:
	@echo "🚀 Generating OpenAPI specification..."
	python scripts/generate_openapi.py

openapi-validate:
	@echo "✅ Validating OpenAPI specification syntax..."
	@python -c "import yaml; yaml.safe_load(open('gpt.openapi.yaml', 'r')); print('✅ OpenAPI YAML syntax is valid')"

openapi-all:
	@echo "🔄 Running complete OpenAPI workflow..."
	@make openapi-validate
	@make openapi-update
	@echo "✅ OpenAPI workflow completed!"

# Help target for OpenAPI commands
openapi-help:
	@echo "OpenAPI Specification Management Commands:"
	@echo "  openapi-validate  - Validate YAML syntax"
	@echo "  openapi-check     - Check for missing endpoints (CI-friendly)"
	@echo "  openapi-update    - Analyze and generate report"
	@echo "  openapi-generate  - Generate new specification"
	@echo "  openapi-all       - Run validation and analysis"
	@echo "  openapi-help      - Show this help"
	@echo ""
	@echo "For more details, see docs/openapi_automation.md"

# Main help target
help:
	@echo "Available commands:"
	@echo "  setup            - Install dependencies"
	@echo "  run              - Run the Flask application"
	@echo "  test             - Run tests"
	@echo "  lint             - Run code linting"
	@echo "  clean            - Clean up generated files"
	@echo "  openapi-help     - Show OpenAPI management commands"
	@echo ""
	@echo "Use 'make openapi-help' for OpenAPI specification management."
