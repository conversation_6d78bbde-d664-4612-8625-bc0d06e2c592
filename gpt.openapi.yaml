openapi: 3.0.0
info:
  title: Go GPT Backend Async API
  description: API documentation for Go GPT Backend's asynchronous endpoints
  version: 1.0.0
servers:
  - url: https://app.dev.addxgo.io/api/gpt

tags:
  - name: Root
    description: Root endpoints
  - name: Conversation
    description: Endpoints for conversation management
  - name: Assistant
    description: Endpoints for OpenAI Assistant integration
  - name: Cache
    description: Endpoints for cache management
  - name: Financial Calendar
    description: Endpoints for financial calendar data
  - name: SEC Filings
    description: Endpoints for SEC filing data
  - name: Earnings Calendar
    description: Endpoints for earnings calendar data
  - name: User Queries
    description: Endpoints for user query management
  - name: Shared Queries
    description: Endpoints for sharing queries
  - name: Models
    description: Endpoints for model information

paths:
  /:
    get:
      summary: Root endpoint
      description: Health check endpoint
      tags:
        - Root
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Go GPT Backend API"

  /pub/healthcheck:
    get:
      summary: Public health check
      description: Public health check endpoint
      tags:
        - Root
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                example: {}

  /models:
    get:
      summary: List available models
      description: List available models with their capabilities and metrics
      tags:
        - Models
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ModelsResponse'

  /query:
    post:
      summary: Handle authenticated query
      description: Process an authenticated query from the user
      tags:
        - Conversation
      security:
        - Auth0: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QueryRequest'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QueryResponse'
            text/event-stream:
              schema:
                type: string
                description: Event stream for streaming responses
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /pub/query:
    post:
      summary: Handle public query
      description: Process a public (unauthenticated) query from the user
      tags:
        - Conversation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/QueryRequest'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/QueryResponse'
            text/event-stream:
              schema:
                type: string
                description: Event stream for streaming responses
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /conversation/new-round:
    post:
      summary: Start new conversation round
      description: Start a new conversation round
      tags:
        - Conversation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SessionRequest'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  session_id:
                    type: string
                  round_id:
                    type: integer
                  message:
                    type: string
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /conversation/stats/{session_id}:
    get:
      summary: Get conversation stats
      description: Get statistics about a conversation
      tags:
        - Conversation
      parameters:
        - name: session_id
          in: path
          required: true
          schema:
            type: string
          description: Session ID for the conversation
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  session_id:
                    type: string
                  message_count:
                    type: integer
                  rounds:
                    type: array
                    items:
                      type: object
                      properties:
                        round_id:
                          type: integer
                        message_count:
                          type: integer
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /conversation/export:
    post:
      summary: Export conversation
      description: Export a conversation
      tags:
        - Conversation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ExportRequest'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  session_id:
                    type: string
                  format:
                    type: string
                  content:
                    type: string
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /conversation/branch:
    post:
      summary: Create conversation branch
      description: Create a new conversation branch
      tags:
        - Conversation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/BranchRequest'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  original_session_id:
                    type: string
                  branch_session_id:
                    type: string
                  branch_name:
                    type: string
                  message:
                    type: string
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /conversation/clear:
    post:
      summary: Clear conversation history
      description: Clear conversation history
      tags:
        - Conversation
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ClearHistoryRequest'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  session_id:
                    type: string
                  round_id:
                    type: integer
                    nullable: true
                  message:
                    type: string
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /assistant:
    post:
      summary: Handle authenticated assistant query
      description: Handle authenticated assistant API queries asynchronously
      tags:
        - Assistant
      security:
        - Auth0: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AssistantQueryRequest'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  response:
                    type: string
                  thread_id:
                    type: string
                  run_id:
                    type: string
            text/event-stream:
              schema:
                type: string
                description: Event stream for streaming responses
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /pub/assistant:
    post:
      summary: Handle public assistant query
      description: Handle public (unauthenticated) assistant API queries asynchronously
      tags:
        - Assistant
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AssistantQueryRequest'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  response:
                    type: string
                  thread_id:
                    type: string
                  run_id:
                    type: string
            text/event-stream:
              schema:
                type: string
                description: Event stream for streaming responses
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /assistant/stream:
    post:
      summary: Stream authenticated assistant response (POST)
      description: Stream the authenticated assistant response (POST method)
      tags:
        - Assistant
      security:
        - Auth0: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AssistantQueryRequest'
      responses:
        '200':
          description: Successful response
          content:
            text/event-stream:
              schema:
                type: string
                description: Event stream for streaming responses
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    get:
      summary: Stream authenticated assistant response (GET)
      description: Stream the authenticated assistant response (GET method). This endpoint is used by EventSource in the browser.
      tags:
        - Assistant
      security:
        - Auth0: []
      parameters:
        - name: query
          in: query
          required: true
          schema:
            type: string
          description: The user's query
        - name: thread_id
          in: query
          required: false
          schema:
            type: string
          description: Optional thread ID for continuing a conversation
        - name: model
          in: query
          required: false
          schema:
            type: string
            default: gpt-4-turbo-preview
          description: The model to use
      responses:
        '200':
          description: Successful response
          content:
            text/event-stream:
              schema:
                type: string
                description: Event stream for streaming responses
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /pub/assistant/stream:
    post:
      summary: Stream public assistant response (POST)
      description: Stream the public (unauthenticated) assistant response (POST method)
      tags:
        - Assistant
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AssistantQueryRequest'
      responses:
        '200':
          description: Successful response
          content:
            text/event-stream:
              schema:
                type: string
                description: Event stream for streaming responses
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    get:
      summary: Stream public assistant response (GET)
      description: Stream the public (unauthenticated) assistant response (GET method). This endpoint is used by EventSource in the browser.
      tags:
        - Assistant
      parameters:
        - name: query
          in: query
          required: true
          schema:
            type: string
          description: The user's query
        - name: thread_id
          in: query
          required: false
          schema:
            type: string
          description: Optional thread ID for continuing a conversation
        - name: model
          in: query
          required: false
          schema:
            type: string
            default: gpt-4-turbo-preview
          description: The model to use
      responses:
        '200':
          description: Successful response
          content:
            text/event-stream:
              schema:
                type: string
                description: Event stream for streaming responses
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /assistant/thread:
    post:
      summary: Create new thread
      description: Create a new conversation thread asynchronously
      tags:
        - Assistant
      security:
        - Auth0: []
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  thread_id:
                    type: string
                  created:
                    type: boolean
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /pub/assistant/thread:
    post:
      summary: Create new thread (public)
      description: Create a new conversation thread asynchronously (public endpoint)
      tags:
        - Assistant
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  thread_id:
                    type: string
                  created:
                    type: boolean
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /assistant/thread/{thread_id}:
    delete:
      summary: Delete thread
      description: Delete a conversation thread asynchronously
      tags:
        - Assistant
      security:
        - Auth0: []
      parameters:
        - name: thread_id
          in: path
          required: true
          schema:
            type: string
          description: The thread ID to delete
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  thread_id:
                    type: string
                  deleted:
                    type: boolean
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /pub/assistant/thread/{thread_id}:
    delete:
      summary: Delete thread (public)
      description: Delete a conversation thread asynchronously (public endpoint)
      tags:
        - Assistant
      parameters:
        - name: thread_id
          in: path
          required: true
          schema:
            type: string
          description: The thread ID to delete
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  thread_id:
                    type: string
                  deleted:
                    type: boolean
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /assistant/thread/{thread_id}/messages:
    get:
      summary: Get thread messages
      description: Get all messages in a thread asynchronously
      tags:
        - Assistant
      security:
        - Auth0: []
      parameters:
        - name: thread_id
          in: path
          required: true
          schema:
            type: string
          description: The thread ID
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  thread_id:
                    type: string
                  messages:
                    type: array
                    items:
                      $ref: '#/components/schemas/ThreadMessage'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /pub/assistant/thread/{thread_id}/messages:
    get:
      summary: Get thread messages (public)
      description: Get all messages in a thread asynchronously (public endpoint)
      tags:
        - Assistant
      parameters:
        - name: thread_id
          in: path
          required: true
          schema:
            type: string
          description: The thread ID
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  thread_id:
                    type: string
                  messages:
                    type: array
                    items:
                      $ref: '#/components/schemas/ThreadMessage'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /assistant/thread/{thread_id}/run/{run_id}/cancel:
    post:
      summary: Cancel run
      description: Cancel an in-progress run asynchronously
      tags:
        - Assistant
      security:
        - Auth0: []
      parameters:
        - name: thread_id
          in: path
          required: true
          schema:
            type: string
          description: The thread ID
        - name: run_id
          in: path
          required: true
          schema:
            type: string
          description: The run ID to cancel
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  thread_id:
                    type: string
                  run_id:
                    type: string
                  cancelled:
                    type: boolean
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /pub/assistant/thread/{thread_id}/run/{run_id}/cancel:
    post:
      summary: Cancel run (public)
      description: Cancel an in-progress run asynchronously (public endpoint)
      tags:
        - Assistant
      parameters:
        - name: thread_id
          in: path
          required: true
          schema:
            type: string
          description: The thread ID
        - name: run_id
          in: path
          required: true
          schema:
            type: string
          description: The run ID to cancel
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  thread_id:
                    type: string
                  run_id:
                    type: string
                  cancelled:
                    type: boolean
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /assistant/cancel:
    post:
      summary: Cancel active run
      description: Cancel the most recent active run for the authenticated user asynchronously
      tags:
        - Assistant
      security:
        - Auth0: []
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  thread_id:
                    type: string
                  run_id:
                    type: string
                  cancelled:
                    type: boolean
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /assistant/debug-stream:
    post:
      summary: Debug streaming
      description: Debug endpoint for testing streaming responses
      tags:
        - Assistant
      responses:
        '200':
          description: Successful response
          content:
            text/event-stream:
              schema:
                type: string
                description: Event stream for streaming responses

  /cache/stats:
    get:
      summary: Get cache statistics
      description: Get cache statistics
      tags:
        - Cache
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  memory_cache_size:
                    type: integer
                  persistent_cache_size:
                    type: integer
                  hit_rate:
                    type: number
                    format: float
                  miss_rate:
                    type: number
                    format: float
                  total_requests:
                    type: integer
                  hits:
                    type: integer
                  misses:
                    type: integer

  /cache/invalidate:
    post:
      summary: Invalidate cached responses
      description: Invalidate cached responses
      tags:
        - Cache
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                provider:
                  type: string
                  description: Provider name to invalidate only responses from that provider
                model:
                  type: string
                  description: Model name to invalidate only responses from that model
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  deleted:
                    type: integer
                  provider:
                    type: string
                    nullable: true
                  model:
                    type: string
                    nullable: true

  /cache/warm:
    post:
      summary: Warm the cache
      description: Warm the cache by preloading frequently accessed items into memory
      tags:
        - Cache
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  message:
                    type: string

  /cache/enforce-limits:
    post:
      summary: Enforce cache size limits
      description: Enforce cache size limits by removing least recently used items
      tags:
        - Cache
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  message:
                    type: string

  /cache/maintenance:
    post:
      summary: Perform cache maintenance
      description: Perform periodic cache maintenance tasks
      tags:
        - Cache
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                  message:
                    type: string

  /cache/config:
    get:
      summary: Get cache configuration
      description: Get cache configuration
      tags:
        - Cache
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  enable_cache:
                    type: boolean
                  cache_ttl:
                    type: integer
                  cache_compression:
                    type: boolean
                  cache_memory_enabled:
                    type: boolean
                  cache_memory_max_items:
                    type: integer
                  cache_memory_ttl:
                    type: integer
                  cache_persistent_enabled:
                    type: boolean
                  cache_persistent_path:
                    type: string
                  cache_lru_policy:
                    type: string
                  cache_max_size_mb:
                    type: integer
                  cache_similarity_threshold:
                    type: number
                    format: float

  /events:
    get:
      summary: Get economic calendar events
      description: Get economic calendar events for the next N days
      tags:
        - Financial Calendar
      security:
        - Auth0: []
      parameters:
        - name: days
          in: query
          required: false
          schema:
            type: integer
            default: 7
          description: Number of days to look ahead
        - name: country
          in: query
          required: false
          schema:
            type: string
          description: Filter by country or region ('United States', 'Europe', 'Asia')
        - name: event_type
          in: query
          required: false
          schema:
            type: string
          description: Filter by event type
        - name: impact
          in: query
          required: false
          schema:
            type: string
            enum: [High, Medium, Low]
          description: Filter by impact level
        - name: time_filter
          in: query
          required: false
          schema:
            type: string
            enum: [upcoming, past]
          description: Filter by time ('upcoming' or 'past')
        - name: page
          in: query
          required: false
          schema:
            type: integer
            default: 1
            minimum: 1
          description: Page number for pagination
        - name: page_size
          in: query
          required: false
          schema:
            type: integer
            default: 30
            minimum: 1
            maximum: 100
          description: Number of items per page
        - name: event_count
          in: query
          required: false
          schema:
            type: integer
            default: 7
          description: Number of event count
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  events:
                    type: array
                    items:
                      $ref: '#/components/schemas/FinancialCalendarEvent'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /pub/events:
    get:
      summary: Get economic calendar events (public)
      description: Public endpoint to get economic calendar events for the next N days and high impact events from previous 4 days
      tags:
        - Financial Calendar
      parameters:
        - name: days
          in: query
          required: false
          schema:
            type: integer
            default: 7
          description: Number of days to look ahead
        - name: country
          in: query
          required: false
          schema:
            type: string
          description: Filter by country or region ('United States', 'Europe', 'Asia')
        - name: event_type
          in: query
          required: false
          schema:
            type: string
          description: Filter by event type
        - name: impact
          in: query
          required: false
          schema:
            type: string
            enum: [High, Medium, Low]
          description: Filter by impact level
        - name: time_filter
          in: query
          required: false
          schema:
            type: string
            enum: [upcoming, past]
          description: Filter by time ('upcoming' or 'past')
        - name: page
          in: query
          required: false
          schema:
            type: integer
            default: 1
            minimum: 1
          description: Page number for pagination
        - name: page_size
          in: query
          required: false
          schema:
            type: integer
            default: 30
            minimum: 1
            maximum: 100
          description: Number of items per page
        - name: event_count
          in: query
          required: false
          schema:
            type: integer
            default: 7
          description: Number of event count
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  events:
                    type: array
                    items:
                      $ref: '#/components/schemas/FinancialCalendarEvent'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /events/{event_id}:
    get:
      summary: Get event details
      description: Get detailed information about a specific economic calendar event
      tags:
        - Financial Calendar
      security:
        - Auth0: []
      parameters:
        - name: event_id
          in: path
          required: true
          schema:
            type: integer
          description: ID of the event
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FinancialCalendarEventDetail'
        '404':
          description: Event not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /pub/events/{event_id}:
    get:
      summary: Get event details (public)
      description: Public endpoint to get detailed information about a specific economic calendar event
      tags:
        - Financial Calendar
      parameters:
        - name: event_id
          in: path
          required: true
          schema:
            type: integer
          description: ID of the event
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/FinancialCalendarEventDetail'
        '404':
          description: Event not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /sec-filings:
    get:
      summary: Get SEC filings
      description: Get SEC filings based on the provided parameters
      tags:
        - SEC Filings
      security:
        - Auth0: []
      parameters:
        - name: ticker
          in: query
          required: false
          schema:
            type: string
          description: Stock ticker symbol
        - name: filing_type
          in: query
          required: false
          schema:
            type: string
          description: Type of filing (e.g., '10-K', '10-Q')
        - name: fiscal_year
          in: query
          required: false
          schema:
            type: integer
          description: Fiscal year of the filing
        - name: fiscal_quarter
          in: query
          required: false
          schema:
            type: integer
          description: Fiscal quarter of the filing
        - name: page
          in: query
          required: false
          schema:
            type: integer
            default: 1
          description: Page number for pagination
        - name: page_size
          in: query
          required: false
          schema:
            type: integer
            default: 20
          description: Number of items per page
        - name: ticker_search
          in: query
          required: false
          schema:
            type: string
          description: Search string to filter tickers (contains search)
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  filings:
                    type: array
                    items:
                      $ref: '#/components/schemas/SECFiling'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /pub/sec-filings:
    get:
      summary: Get SEC filings (public)
      description: Public endpoint to get SEC filings based on the provided parameters
      tags:
        - SEC Filings
      parameters:
        - name: ticker
          in: query
          required: false
          schema:
            type: string
          description: Stock ticker symbol
        - name: filing_type
          in: query
          required: false
          schema:
            type: string
          description: Type of filing (e.g., '10-K', '10-Q')
        - name: fiscal_year
          in: query
          required: false
          schema:
            type: integer
          description: Fiscal year of the filing
        - name: fiscal_quarter
          in: query
          required: false
          schema:
            type: integer
          description: Fiscal quarter of the filing
        - name: page
          in: query
          required: false
          schema:
            type: integer
            default: 1
          description: Page number for pagination
        - name: page_size
          in: query
          required: false
          schema:
            type: integer
            default: 20
          description: Number of items per page
        - name: ticker_search
          in: query
          required: false
          schema:
            type: string
          description: Search string to filter tickers (contains search)
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  filings:
                    type: array
                    items:
                      $ref: '#/components/schemas/SECFiling'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /sec-filings/{filing_id}:
    get:
      summary: Get filing details
      description: Get detailed information about a specific SEC filing
      tags:
        - SEC Filings
      security:
        - Auth0: []
      parameters:
        - name: filing_id
          in: path
          required: true
          schema:
            type: integer
          description: ID of the filing
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SECFilingDetail'
        '404':
          description: Filing not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /pub/sec-filings/{filing_id}:
    get:
      summary: Get filing details (public)
      description: Public endpoint to get detailed information about a specific SEC filing
      tags:
        - SEC Filings
      parameters:
        - name: filing_id
          in: path
          required: true
          schema:
            type: integer
          description: ID of the filing
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SECFilingDetail'
        '404':
          description: Filing not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /get-sec-filing:
    get:
      summary: Get SEC filings with special behaviors
      description: Get SEC filings with special behaviors for 8-K filings
      tags:
        - SEC Filings
      security:
        - Auth0: []
      parameters:
        - name: ticker
          in: query
          required: false
          schema:
            type: string
          description: Stock ticker symbol
        - name: filing_type
          in: query
          required: false
          schema:
            type: string
          description: Type of filing (e.g., '10-K', '10-Q', '8-K')
        - name: fiscal_year
          in: query
          required: false
          schema:
            type: integer
          description: Fiscal year of the filing
        - name: fiscal_quarter
          in: query
          required: false
          schema:
            type: integer
          description: Fiscal quarter of the filing
        - name: page
          in: query
          required: false
          schema:
            type: integer
            default: 1
          description: Page number for pagination
        - name: page_size
          in: query
          required: false
          schema:
            type: integer
            default: 20
          description: Number of items per page
        - name: ticker_search
          in: query
          required: false
          schema:
            type: string
          description: Search string to filter tickers (contains search)
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  filings:
                    type: array
                    items:
                      $ref: '#/components/schemas/SECFiling'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /pub/earnings/events:
    get:
      summary: Get earnings calendar events (public)
      description: Public endpoint to get earnings calendar events for the next N days and notable events from previous 4 days
      tags:
        - Earnings Calendar
      parameters:
        - name: days
          in: query
          required: false
          schema:
            type: integer
            default: 7
          description: Number of days to look ahead
        - name: company
          in: query
          required: false
          schema:
            type: string
          description: Filter by company name (supports partial matching)
        - name: time_filter
          in: query
          required: false
          schema:
            type: string
            enum: [upcoming, past]
          description: Filter by time ('upcoming' or 'past')
        - name: page
          in: query
          required: false
          schema:
            type: integer
            default: 1
            minimum: 1
          description: Page number for pagination
        - name: page_size
          in: query
          required: false
          schema:
            type: integer
            default: 10
            minimum: 1
            maximum: 100
          description: Number of items per page
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  events:
                    type: array
                    items:
                      $ref: '#/components/schemas/EarningsCalendarEvent'
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /user_query:
    get:
      summary: Get user queries
      description: Get queries for a specific user with pagination
      tags:
        - User Queries
      security:
        - Auth0: []
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
            default: 1
            minimum: 1
          description: Page number (starting from 1)
        - name: page_size
          in: query
          required: false
          schema:
            type: integer
            default: 10
            minimum: 1
            maximum: 100
          description: Number of items per page
        - name: search
          in: query
          required: false
          schema:
            type: string
          description: Search term to match against user_query (case-insensitive)
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserQueriesResponse'
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /user_query/{id}:
    patch:
      summary: Update user query read status
      description: Update the read status of a user query from 0 to 1
      tags:
        - User Queries
      security:
        - Auth0: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: User query ID
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Query not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    delete:
      summary: Delete user query
      description: Delete a user query
      tags:
        - User Queries
      security:
        - Auth0: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: User query ID
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Query not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /ticker_facts/{ticker}:
    get:
      summary: Get ticker financial facts
      description: Get financial facts for a specific ticker
      tags:
        - User Queries
      security:
        - Auth0: []
      parameters:
        - name: ticker
          in: path
          required: true
          schema:
            type: string
          description: The ticker symbol (e.g., 'AAPL', 'MSFT')
        - name: query_id
          in: query
          required: false
          schema:
            type: integer
          description: Query ID to get market information from
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TickerFactsResponse'
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /share/query:
    post:
      summary: Create shareable link for queries
      description: Create a shareable link for one or more queries
      tags:
        - Shared Queries
      security:
        - Auth0: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateShareRequest'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ShareResponse'
        '400':
          description: Bad request
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Query not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /share/my-shares:
    get:
      summary: Get user's shares
      description: Get all shares created by the authenticated user
      tags:
        - Shared Queries
      security:
        - Auth0: []
      parameters:
        - name: page
          in: query
          required: false
          schema:
            type: integer
            default: 1
            minimum: 1
          description: Page number
        - name: page_size
          in: query
          required: false
          schema:
            type: integer
            default: 10
            minimum: 1
            maximum: 50
          description: Items per page
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  shares:
                    type: array
                    items:
                      type: object
                      properties:
                        share_id:
                          type: string
                        title:
                          type: string
                        created_at:
                          type: string
                          format: date-time
                        expires_at:
                          type: string
                          format: date-time
                        query_count:
                          type: integer
                  pagination:
                    $ref: '#/components/schemas/Pagination'
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /share/{share_id}:
    delete:
      summary: Revoke shared query
      description: Revoke a shared query
      tags:
        - Shared Queries
      security:
        - Auth0: []
      parameters:
        - name: share_id
          in: path
          required: true
          schema:
            type: string
          description: UUID of the share to revoke
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                type: object
                properties:
                  success:
                    type: boolean
                  message:
                    type: string
        '401':
          description: Authentication required
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Share not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /pub/shared/{share_id}:
    get:
      summary: Get shared query (public)
      description: Get a shared query by share ID - no authentication required
      tags:
        - Shared Queries
      parameters:
        - name: share_id
          in: path
          required: true
          schema:
            type: string
          description: UUID of the shared query
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PublicQueryResponse'
        '404':
          description: Shared query not found
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '410':
          description: Shared query has expired or been revoked
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Server error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

components:
  securitySchemes:
    Auth0:
      type: apiKey
      name: auth0_sub
      in: header
      description: Auth0 subject identifier for authenticated requests

  schemas:
    QueryRequest:
      type: object
      properties:
        query:
          type: string
          description: The user's query
        session_id:
          type: string
          description: Optional session ID for continuing a conversation
        provider_model:
          type: string
          description: Optional provider and model combination (e.g., 'openai/gpt-4')
        web_search:
          type: boolean
          default: false
          description: Whether to enable web search
        stream:
          type: boolean
          default: false
          description: Whether to stream the response
        new_round:
          type: boolean
          default: false
          description: Whether to start a new conversation round
        include_summary:
          type: boolean
          default: false
          description: Whether to include a summary of the conversation
        max_tokens:
          type: integer
          description: Maximum number of tokens to generate
        enable_functions:
          type: boolean
          default: false
          description: Whether to enable function calling
        auto_functions:
          type: boolean
          default: false
          description: Whether to automatically select functions
        functions:
          type: array
          items:
            type: string
          description: List of function names to make available
      required:
        - query

    QueryResponse:
      type: object
      properties:
        response:
          type: string
          description: The model's response
        session_id:
          type: string
          description: Session ID for the conversation
        round_id:
          type: integer
          description: Round ID for the conversation
        metadata:
          type: object
          description: Additional metadata about the response
        sources:
          type: array
          items:
            type: object
            properties:
              title:
                type: string
              url:
                type: string
              snippet:
                type: string
          description: Sources used for the response (if web search was enabled)

    AssistantQueryRequest:
      type: object
      properties:
        query:
          type: string
          description: The user's query
        thread_id:
          type: string
          description: Optional thread ID for continuing a conversation
        model:
          type: string
          default: gpt-4-turbo-preview
          description: The model to use
        stream:
          type: boolean
          default: false
          description: Whether to stream the response
      required:
        - query

    SessionRequest:
      type: object
      properties:
        session_id:
          type: string
          description: Session ID for the conversation
      required:
        - session_id

    BranchRequest:
      type: object
      properties:
        session_id:
          type: string
          description: Session ID for the conversation
        branch_name:
          type: string
          description: Name for the new branch
        from_message_index:
          type: integer
          description: Optional message index to branch from
      required:
        - session_id
        - branch_name

    ExportRequest:
      type: object
      properties:
        session_id:
          type: string
          description: Session ID for the conversation
        format:
          type: string
          enum: [text, markdown, html, json]
          default: markdown
          description: Format for the exported conversation
      required:
        - session_id

    ClearHistoryRequest:
      type: object
      properties:
        session_id:
          type: string
          description: Session ID for the conversation
        round_id:
          type: integer
          description: Optional round ID to clear only messages from that round
      required:
        - session_id

    ThreadMessage:
      type: object
      properties:
        id:
          type: string
          description: Message ID
        role:
          type: string
          enum: [user, assistant]
          description: Role of the message sender
        content:
          type: array
          items:
            type: object
            properties:
              type:
                type: string
                enum: [text]
              text:
                type: object
                properties:
                  value:
                    type: string
                  annotations:
                    type: array
                    items:
                      type: object
          description: Content of the message
        created_at:
          type: integer
          description: Unix timestamp of when the message was created

    FinancialCalendarEvent:
      type: object
      properties:
        id:
          type: integer
          description: Event ID
        date:
          type: string
          format: date-time
          description: Date and time of the event
        country:
          type: string
          description: Country the event is related to
        event:
          type: string
          description: Name of the event
        impact:
          type: string
          enum: [High, Medium, Low]
          description: Impact level of the event
        previous:
          type: string
          description: Previous value
        forecast:
          type: string
          description: Forecasted value
        actual:
          type: string
          description: Actual value (if available)

    FinancialCalendarEventDetail:
      type: object
      properties:
        id:
          type: integer
          description: Event ID
        date:
          type: string
          format: date-time
          description: Date and time of the event
        country:
          type: string
          description: Country the event is related to
        event:
          type: string
          description: Name of the event
        impact:
          type: string
          enum: [High, Medium, Low]
          description: Impact level of the event
        previous:
          type: string
          description: Previous value
        forecast:
          type: string
          description: Forecasted value
        actual:
          type: string
          description: Actual value (if available)
        description:
          type: string
          description: Detailed description of the event
        source:
          type: string
          description: Source of the event data
        related_assets:
          type: array
          items:
            type: string
          description: Assets related to this event

    SECFiling:
      type: object
      properties:
        id:
          type: integer
          description: Filing ID
        ticker:
          type: string
          description: Stock ticker symbol
        company_name:
          type: string
          description: Name of the company
        filing_type:
          type: string
          description: Type of filing (e.g., '10-K', '10-Q')
        filing_date:
          type: string
          format: date
          description: Date the filing was submitted
        fiscal_year:
          type: integer
          description: Fiscal year of the filing
        fiscal_quarter:
          type: integer
          description: Fiscal quarter of the filing
        url:
          type: string
          description: URL to the filing on the SEC website

    SECFilingDetail:
      type: object
      properties:
        id:
          type: integer
          description: Filing ID
        ticker:
          type: string
          description: Stock ticker symbol
        company_name:
          type: string
          description: Name of the company
        filing_type:
          type: string
          description: Type of filing (e.g., '10-K', '10-Q')
        filing_date:
          type: string
          format: date
          description: Date the filing was submitted
        fiscal_year:
          type: integer
          description: Fiscal year of the filing
        fiscal_quarter:
          type: integer
          description: Fiscal quarter of the filing
        url:
          type: string
          description: URL to the filing on the SEC website
        sections:
          type: array
          items:
            type: object
            properties:
              title:
                type: string
                description: Section title
              content:
                type: string
                description: Section content
          description: Sections of the filing

    Pagination:
      type: object
      properties:
        page:
          type: integer
          description: Current page number
        page_size:
          type: integer
          description: Number of items per page
        total_pages:
          type: integer
          description: Total number of pages
        total_items:
          type: integer
          description: Total number of items

    ModelsResponse:
      type: object
      additionalProperties:
        type: object
        properties:
          capabilities:
            type: array
            items:
              type: string
            description: List of model capabilities
          avg_latency:
            type: number
            format: float
            description: Average latency in seconds
          error_rate:
            type: number
            format: float
            description: Error rate as a decimal
          cost_per_token:
            type: number
            format: float
            description: Cost per token

    EarningsCalendarEvent:
      type: object
      properties:
        id:
          type: integer
          description: Event ID
        date:
          type: string
          format: date-time
          description: Date and time of the earnings event
        company:
          type: string
          description: Company name
        ticker:
          type: string
          description: Stock ticker symbol
        time:
          type: string
          description: Time of the earnings call
        eps_estimate:
          type: number
          format: float
          description: Estimated earnings per share
        eps_actual:
          type: number
          format: float
          description: Actual earnings per share (if available)
        revenue_estimate:
          type: number
          format: float
          description: Estimated revenue
        revenue_actual:
          type: number
          format: float
          description: Actual revenue (if available)

    UserQuery:
      type: object
      properties:
        id:
          type: integer
          description: Query ID
        user_query:
          type: string
          description: The user's query text
        created_at:
          type: string
          format: date-time
          description: When the query was created
        read_status:
          type: integer
          description: Read status (0 = unread, 1 = read)
        response:
          type: string
          description: The AI response to the query
        metadata:
          type: object
          description: Additional metadata about the query

    UserQueriesResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Whether the request was successful
        queries:
          type: array
          items:
            $ref: '#/components/schemas/UserQuery'
          description: List of user queries
        pagination:
          $ref: '#/components/schemas/Pagination'

    TickerFactsResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Whether the request was successful
        ticker:
          type: string
          description: The ticker symbol
        data:
          type: object
          description: Financial facts data for the ticker
        error:
          type: string
          description: Error message if any

    CreateShareRequest:
      type: object
      properties:
        user_query_ids:
          type: array
          items:
            type: integer
          description: List of query IDs to share
        title:
          type: string
          description: Title for the shared content
        description:
          type: string
          description: Optional description
        expires_in_days:
          type: integer
          default: 30
          description: Number of days until the share expires
      required:
        - user_query_ids

    ShareResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Whether the share was created successfully
        share_id:
          type: string
          description: UUID of the created share
        expires_at:
          type: string
          format: date-time
          description: When the share expires

    PublicQueryResponse:
      type: object
      properties:
        success:
          type: boolean
          description: Whether the request was successful
        data:
          type: object
          description: The shared query data
        error:
          type: string
          description: Error message if any

    ErrorResponse:
      type: object
      properties:
        detail:
          type: string
          description: Error message
