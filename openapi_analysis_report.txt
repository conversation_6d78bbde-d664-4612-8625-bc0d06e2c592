============================================================
OpenAPI Specification Analysis Report
============================================================
Generated: 2025-06-05 14:19:52

📊 Summary:
  • Endpoints in codebase: 54
  • Endpoints in OpenAPI spec: 40
  • Missing from spec: 20
  • Extra in spec: 6

❌ Missing from OpenAPI specification:
  • DELETE /assistant/thread/<thread_id>
  • DELETE /share/{share_id}
  • DELETE /user_query/{id}
  • GET /assistant/thread/<thread_id>/messages
  • GET /config
  • GET /conversation/stats/<session_id>
  • GET /models
  • GET /pub/earnings/events
  • GET /pub/shared/{share_id}
  • GET /share/my-shares
  • GET /stats
  • GET /ticker_facts/{ticker}
  • GET /user_query
  • PATCH /user_query/{id}
  • POST /assistant/thread/<thread_id>/run/<run_id>/cancel
  • POST /enforce-limits
  • POST /invalidate
  • POST /maintenance
  • POST /share/query
  • POST /warm

⚠️  In OpenAPI spec but not found in codebase:
  • GET /cache/config
  • GET /cache/stats
  • POST /cache/enforce-limits
  • POST /cache/invalidate
  • POST /cache/maintenance
  • POST /cache/warm

💡 Recommendations:
  • Add missing endpoints to the OpenAPI specification
  • Review extra endpoints - they may be deprecated or incorrectly detected

============================================================