"""
Async service for managing shared queries.
"""
import datetime
from typing import Optional, Dict, Any, List
from sqlalchemy import select, and_, func, text
from sqlalchemy.dialects import postgresql

from models.shared_query import SharedQuery
from models.query import Query
from models.user_query import UserQuery
from utils.multi_schema_db_manager import async_session_context
from utils.logging import logger

class AsyncSharedQueryService:
    """Service for managing shared queries."""
    
    def __init__(self):
        # Set schema for models
        SharedQuery.set_schema('user_data')
        Query.set_schema('user_data')
        UserQuery.set_schema('user_data')

    def _log_sql_query(self, query):
        """Helper method to log SQL query with parameters."""
        try:
            compiled = query.compile(dialect=postgresql.dialect())
            sql_str = str(compiled)
            
            # Get the bound parameters
            bound_params = compiled.params
            
            # Replace parameter placeholders with actual values
            for param_name, param_value in bound_params.items():
                placeholder = f"%({param_name})s"
                if isinstance(param_value, str):
                    sql_str = sql_str.replace(placeholder, f"'{param_value}'")
                elif isinstance(param_value, bool):
                    sql_str = sql_str.replace(placeholder, str(param_value).lower())
                elif isinstance(param_value, (list, tuple)):
                    sql_str = sql_str.replace(placeholder, f"'{param_value}'")
                else:
                    sql_str = sql_str.replace(placeholder, str(param_value))
            
            logger.info(f"SQL Query: {sql_str}")
        except Exception as e:
            logger.info(f"SQL Query: {compiled}")
            logger.error(f"Failed to substitute parameters: {e}")

    async def create_multi_share(
        self,
        user_query_ids: List[int],
        user_id: str,
        title: Optional[str] = None,
        expires_in_days: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Create a new share for multiple queries.

        Args:
            query_ids: List of query IDs to share
            user_id: User ID (auth0_sub) of the query owner
            title: Optional custom title
            description: Optional description
            expires_in_days: Optional expiration in days

        Returns:
            Dict with success status and share data
        """
        try:
            async with async_session_context('user_data') as session:
                # Verify user owns all queries
                query_result = await session.execute(
                    select(UserQuery)
                    .where(
                        and_(
                            UserQuery.id.in_(user_query_ids),
                            UserQuery.uid == user_id
                        )
                    )
                )
                found_queries = query_result.scalars().all()
                found_user_query_ids = [q.id for q in found_queries]

                # Check if all requested queries were found
                missing_user_query_ids = set(user_query_ids).difference(set(found_user_query_ids))
                if missing_user_query_ids:
                    return {
                        "success": False,
                        "error": f"Queries not found or access denied: {list(missing_user_query_ids)}"
                    }

                # Check if there's already an existing share for this exact set of queries
                # Convert to JSON string and use raw SQL for exact comparison
                import json
                user_query_ids_json = json.dumps(found_user_query_ids)

                query = select(SharedQuery).where(
                    and_(
                        SharedQuery.user_id == user_id,
                        SharedQuery.is_active == True,
                        # Use raw SQL for exact JSON comparison
                        text("JSON_UNQUOTE(JSON_EXTRACT(user_query_ids, '$')) = :user_query_ids_json")
                    )
                ).params(user_query_ids_json=user_query_ids_json)
                
                # Log the SQL query with parameters
                self._log_sql_query(query)
                
                existing_share = await session.execute(query)
                existing_share = existing_share.scalar_one_or_none()
                if existing_share:
                    # Found exact match
                    return {
                        "success": True,
                        "share_id": existing_share.share_id,
                        "expires_at": existing_share.expires_at.isoformat() if existing_share.expires_at else None,
                    }

                # Calculate expiration
                expires_at = None
                if expires_in_days:
                    expires_at = datetime.datetime.now() + datetime.timedelta(days=expires_in_days)

                # Generate a single share_id for all queries
                share_id = SharedQuery.generate_share_id()

                # Create a single share record with JSON array of query_ids
                shared_query = SharedQuery(
                    share_id=share_id,
                    user_query_ids=found_user_query_ids,
                    user_id=user_id,
                    title=title,
                    expires_at=expires_at
                )

                session.add(shared_query)
                await session.commit()

                return {
                    "success": True,
                    "share_id": share_id,
                    "expires_at": expires_at.isoformat() if expires_at else None,
                }

        except Exception as e:
            logger.error(f"Error creating multi-share: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_shared_query(self, share_id: str) -> Dict[str, Any]:
        """
        Get shared queries by share_id for public access.
        Now works with JSON array of query_ids.

        Args:
            share_id: The UUID share identifier

        Returns:
            Dict with query data or error
        """
        try:
            async with async_session_context('user_data') as session:
                # Get the shared query record
                shared_result = await session.execute(
                    select(SharedQuery).where(SharedQuery.share_id == share_id)
                )
                shared_query = shared_result.scalar_one_or_none()

                if not shared_query:
                    return {
                        "success": False,
                        "error": "Shared query not found"
                    }

                # Check if accessible
                if not shared_query.is_accessible():
                    return {
                        "success": False,
                        "error": "Shared query is no longer accessible"
                    }
                
                user_query_result = await session.execute(
                    select(UserQuery)
                    .where(
                        and_(
                            UserQuery.id.in_(shared_query.user_query_ids),
                        )
                    )
                )
                found_user_queries = user_query_result.scalars().all()
                found_query_ids = [q.query_id for q in found_user_queries]
                found_user_query_id_map = {q.id: q.query_id for q in found_user_queries}


                # Get all queries from the JSON array
                query_result = await session.execute(
                    select(Query).where(Query.id.in_(found_query_ids))
                    .order_by(Query.created_at.asc())  # Order by creation time
                )
                queries_list = query_result.scalars().all()

                if not queries_list:
                    return {
                        "success": False,
                        "error": "Associated queries not found"
                    }

                queries_map = {q.id: q for q in queries_list}

                # Increment view count
                shared_query.increment_view_count()
                await session.commit()

                queries = []
                for user_query_id in shared_query.user_query_ids:
                    query_id = found_user_query_id_map[user_query_id]
                    query = queries_map[query_id]
                    query_data = {
                        "id": query.id,
                        "user_query": query.user_query,
                        "llm_answer": query.llm_answer,
                        "model": query.model,
                        "created_at": query.created_at.isoformat() if query.created_at else None,
                        # Filter resources to remove sensitive data
                        "resources": self._sanitize_resources(query.resources) if query.resources else []
                    }
                    queries.append(query_data)


                # Return sanitized data for public consumption
                return {
                    "success": True,
                    "data": {
                        "share_info": shared_query.to_public_dict(),
                        "queries": queries,
                    }
                }

        except Exception as e:
            logger.error(f"Error getting shared query: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_user_shares(self, user_id: str, page: int = 1, page_size: int = 10) -> Dict[str, Any]:
        """Get all shares created by a user."""
        try:
            async with async_session_context('user_data') as session:
                offset = (page - 1) * page_size

                # Get total count
                count_result = await session.execute(
                    select(func.count(SharedQuery.id))
                    .where(SharedQuery.user_id == user_id)
                )
                total_count = count_result.scalar()

                # Get shares
                result = await session.execute(
                    select(SharedQuery)
                    .where(SharedQuery.user_id == user_id)
                    .order_by(SharedQuery.created_at.desc())
                    .offset(offset)
                    .limit(page_size)
                )

                shares = []
                for shared_query in result.scalars():
                    share_data = shared_query.to_dict()
                    # Get preview of first query
                    if shared_query.user_query_ids:
                        first_query_result = await session.execute(
                            select(Query).where(Query.id == shared_query.user_query_ids[0])
                        )
                        first_query = first_query_result.scalar_one_or_none()
                        if first_query:
                            share_data["query_preview"] = first_query.user_query[:100] + "..." if len(first_query.user_query) > 100 else first_query.user_query
                        else:
                            share_data["query_preview"] = "Query not found"
                    else:
                        share_data["query_preview"] = "No queries"

                    shares.append(share_data)

                return {
                    "success": True,
                    "shares": shares,
                    "pagination": {
                        "total_count": total_count,
                        "page": page,
                        "page_size": page_size,
                        "total_pages": (total_count + page_size - 1) // page_size
                    }
                }

        except Exception as e:
            logger.error(f"Error getting user shares: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def revoke_share(self, share_id: str, user_id: str) -> Dict[str, Any]:
        """Revoke/deactivate a share."""
        try:
            async with async_session_context('user_data') as session:
                result = await session.execute(
                    select(SharedQuery).where(
                        and_(
                            SharedQuery.share_id == share_id,
                            SharedQuery.user_id == user_id
                        )
                    )
                )
                shared_query = result.scalar_one_or_none()
                
                if not shared_query:
                    return {
                        "success": False,
                        "error": "Share not found or access denied"
                    }
                
                shared_query.is_active = False
                await session.commit()
                
                return {
                    "success": True,
                    "message": "Share revoked successfully"
                }
                
        except Exception as e:
            logger.error(f"Error revoking share: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _sanitize_resources(self, resources: List[Dict]) -> List[Dict]:
        """Remove sensitive information from resources for public sharing."""
        sanitized = []
        for resource in resources:
            # Only include safe resource types and remove sensitive data
            if resource.get("function_name") in ["web_search", "get_stock_price", "get_company_info"]:
                sanitized_resource = {
                    "function_name": resource.get("function_name"),
                    "timestamp": resource.get("timestamp"),
                    # Include only non-sensitive parts of output
                    "summary": "External data source used"
                }
                sanitized.append(sanitized_resource)
        return sanitized
