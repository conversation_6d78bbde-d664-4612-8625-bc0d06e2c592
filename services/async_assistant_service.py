"""
Async service for handling OpenAI Assistant API interactions.
"""
import asyncio
import time
import json
import traceback
import datetime
from typing import Dict, Any, Optional, List, AsyncGenerator
from openai import Async<PERSON>penAI
from config.settings import settings
from utils.logging import logger
from utils.assistant_utils import initialize_assistant_async
from utils.async_redis_client import get_async_redis_client
from utils.query_classifier import enhance_query_with_context, classify_query_type
from utils.thread_pruning import ThreadPruningManager
from providers.async_openai_assistant_provider import AsyncOpenAIAssistantProvider
from utils.sample_functions import query_ticker_concepts, search, refine_search, get_search_history, get_tickers_dividend, query_stocks, query_sec_filings, query_sec_filing_sections

class AsyncAssistantService:
    """
    Async service for handling OpenAI Assistant API interactions.
    """
    def __init__(self):
        self.provider = AsyncOpenAIAssistantProvider()
        self.client = self.provider.client
        self.pruning_manager = ThreadPruningManager(self.client)
        
        # Start background pruning task
        asyncio.create_task(self.pruning_manager.start_background_pruning())
        
    async def _get_or_create_thread(self, thread_id: Optional[str], auth0_sub: Optional[str]) -> str:
        """
        Get an existing thread ID from Redis or create a new one.
        
        Args:
            thread_id: Optional thread ID provided by the user
            auth0_sub: Optional user identifier for thread persistence
            
        Returns:
            The thread ID to use
        """
        # If thread_id is already provided, use it
        if thread_id:
            # Prune the thread if needed before using it
            await self.pruning_manager.pre_request_prune(thread_id)
            return thread_id
            
        # If auth0_sub is provided but thread_id is not, try to get thread_id from Redis
        if auth0_sub:
            redis_client = await get_async_redis_client()
            stored_thread_id = await redis_client.get(f"thread:{auth0_sub}")
            if stored_thread_id:
                # No need to decode as redis_client is configured with decode_responses=True
                thread_id = stored_thread_id
                logger.info(f"Retrieved thread ID {thread_id} for user {auth0_sub} from Redis")
                
                # Prune the thread if needed before using it
                await self.pruning_manager.pre_request_prune(thread_id)
                
                return thread_id
                
            # If no thread_id in Redis, create a new one and store it
            thread = await self.create_thread()
            if thread.get('created') and thread.get('thread_id'):
                new_thread_id = thread.get('thread_id')
                await redis_client.set(f"thread:{auth0_sub}", new_thread_id)
                logger.info(f"Created and stored new thread ID {new_thread_id} for user {auth0_sub} in Redis")
                return new_thread_id
        
        # If no auth0_sub or Redis retrieval failed, create a thread without storing it
        thread = await self.create_thread()
        if thread.get('created') and thread.get('thread_id'):
            return thread.get('thread_id')
            
        # If all else fails, return None and let the provider handle it
        return None
    
    async def stream_query(self,
                          user_query: str,
                          thread_id: Optional[str] = None,
                          model: str = "gpt-4-turbo-preview",
                          auth0_sub: Optional[str] = None) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Process a user query using the OpenAI Assistant API and stream the response.
        First checks if the query exists in the database with status "completed".
        If it does, returns the stored answer in streaming mode.
        Otherwise, makes a new request to the OpenAI API.
        
        Args:
            user_query: The user's query text
            thread_id: Optional thread ID for continuing a conversation
            model: The model to use (default: gpt-4-turbo-preview)
            auth0_sub: Optional user identifier for thread persistence
            
        Returns:
            AsyncGenerator yielding response chunks
        """
        try:
            # Get or create thread ID
            thread_id = await self._get_or_create_thread(thread_id, auth0_sub)
            
            # Check if this query already exists in the database with status "completed"
            from utils.multi_schema_db_manager import async_session_context
            from models.query import Query
            from sqlalchemy import text
            
            existing_query = None
            async with async_session_context('user_data') as session:
                # Look for an exact match of the query that is cacheable
                result = await session.execute(
                    text("SELECT * FROM query WHERE user_query = :user_query AND status = :status AND cacheable = TRUE"),
                    {"user_query": user_query, "status": "completed"}
                )
                existing_query = result.first()
                
                if existing_query:
                    logger.info(f"Found existing completed query in database for thread {thread_id}")
                    
                    # If auth0_sub is provided, create a user_query record
                    if auth0_sub:
                        try:
                            # Import the UserQuery model
                            from models.user_query import UserQuery
                            
                            # Create a new user_query record
                            user_query = UserQuery(
                                uid=auth0_sub,
                                query_id=existing_query.id,
                                query_time=datetime.datetime.now(),
                                status="pending"
                            )
                            
                            # Add and commit the record
                            session.add(user_query)
                            await session.commit()
                            
                            # Yield user_query_confirmed message immediately after successful creation
                            user_query_confirmed_message = {
                                "type": "user_query_confirmed",
                                "value": user_query.id,
                                "thread_id": thread_id,
                                "timestamp": time.time()
                            }
                            yield user_query_confirmed_message

                            logger.info(f"Created user_query record for user {auth0_sub} and query {existing_query.id}")
                        except Exception as e:
                            logger.error(f"Error creating user_query record: {str(e)}")
                            # Continue with returning cached response even if record creation fails

            # If we found a completed query, return its answer in streaming mode
            if existing_query and existing_query.llm_answer:
                logger.info(f"Returning cached answer from database for thread {thread_id}")
                
                # Add the user query and assistant answer to the thread as messages
                # This ensures proper context for subsequent questions
                try:
                    # Add user query to thread
                    today_date = datetime.datetime.now().strftime("%Y %B %-d")
                    user_content = f"{existing_query.user_query}\n: {existing_query.system_prompt}. {settings.OPENAI_ASSISTANT_FIXED_PROMPT}"
                    await self.client.beta.threads.messages.create(
                        thread_id=thread_id,
                        role="user",
                        content=user_content
                    )
                    logger.info(f"Added user query to thread {thread_id} for context")
                    
                    # Add assistant answer to thread
                    await self.client.beta.threads.messages.create(
                        thread_id=thread_id,
                        role="assistant",
                        content=existing_query.llm_answer
                    )
                    logger.info(f"Added cached assistant answer to thread {thread_id} for context")
                except Exception as e:
                    logger.error(f"Error adding cached messages to thread: {str(e)}")
                    # Continue with returning cached response even if adding messages fails
                
                # Helper function to get appropriate progress messages for different function types
                def get_function_progress_message(function_name):
                    """Return an appropriate progress message based on the function name."""
                    if function_name == "search":
                        return "Collecting information via Deepsearch..."
                    elif function_name == "refine_search":
                        return "Refining search results..."
                    elif function_name == "get_search_history":
                        return "Retrieving search history..."
                    elif function_name == "get_tickers_dividend":
                        return "Fetching dividend information..."
                    elif function_name == "query_stocks":
                        return "Querying stock information..."
                    elif function_name == "query_ticker_concepts":
                        return "Getting company concepts information..."
                    elif function_name == "query_sec_filings":
                        return "Retrieving SEC filings..."
                    elif function_name == "query_sec_filing_sections":
                        return "Analyzing SEC filing sections..."
                    elif function_name == "provide_response_metadata":
                        return "Processing metadata..."
                    else:
                        return f"Processing {function_name}..."
                
                async def cached_response_generator():
                    # Track start time to ensure we complete within 8 seconds
                    start_time = time.time()
                    max_total_time = 8.0  # Maximum time in seconds for the entire process
                                        
                    # Send a progress message
                    progress_message = {
                        "type": "progress",
                        "content": "Retrieving your answer...",
                        "thread_id": thread_id,
                        "timestamp": time.time()
                    }
                    yield progress_message
                    
                    # Check if we have resources (function calls) to replay
                    resources = existing_query.resources or []
                    
                    # If resources is a string (JSON), parse it
                    if isinstance(resources, str):
                        try:
                            resources = json.loads(resources)
                            logger.info(f"Successfully parsed resources JSON string into a list of {len(resources)} items")
                        except json.JSONDecodeError as e:
                            logger.error(f"Failed to parse resources as JSON: {str(e)}")
                            resources = []
                    
                    # Ensure resources is a list
                    if not isinstance(resources, list):
                        logger.warning(f"Resources is not a list, converting to list: {type(resources)}")
                        if resources:
                            # If it's a non-empty value but not a list, wrap it in a list
                            resources = [resources]
                        else:
                            # If it's empty or None, use an empty list
                            resources = []
                    
                    # Calculate timing parameters based on number of resources
                    num_resources = len(resources)
                    
                    # Reserve time for streaming the answer (3-4 seconds)
                    answer_streaming_time = min(4.0, max_total_time * 0.5)
                    
                    # Calculate available time for function calls
                    available_function_time = max_total_time - answer_streaming_time - 0.5  # 0.5s buffer
                    
                    # Calculate delay between function calls
                    if num_resources > 0:
                        # Each function call needs time for before and after messages
                        per_function_time = available_function_time / num_resources
                        function_delay = max(0.2, min(1.0, per_function_time / 3))  # Between 0.2 and 1.0 seconds
                    else:
                        function_delay = 0
                    
                    # Simulate the run creation
                    run_created_message = {
                        "type": "progress",
                        "content": "Assistant is thinking...",
                        "thread_id": thread_id,
                        "run_id": existing_query.run_id,
                        "timestamp": time.time()
                    }
                    yield run_created_message
                    await asyncio.sleep(0.5)
                    
                    # Process each function call in resources
                    function_call_count = {}
                    for i, resource in enumerate(resources):
                        function_name = resource.get("function_name", "unknown_function")
                        function_call_count[function_name] = function_call_count.get(function_name, 0) + 1
                    logger.info(f"cache_function_call_count: {function_call_count}")

                    for i, resource in enumerate(resources):
                        # Check if we're approaching our time limit
                        # elapsed_time = time.time() - start_time
                        # if elapsed_time > (max_total_time - answer_streaming_time - 0.5):
                        #     logger.info(f"Skipping remaining function calls to stay within time limit. Elapsed: {elapsed_time}s")
                        #     break
                        
                        # Ensure resource is a dictionary
                        if not isinstance(resource, dict):
                            logger.warning(f"Resource at index {i} is not a dictionary: {type(resource)}, skipping")
                            continue
                            
                        function_name = resource.get("function_name", "unknown_function")
                        
                        # Send progress message before function call
                        before_function_message = {
                            "type": "progress",
                            "content": get_function_progress_message(function_name),
                            "thread_id": thread_id,
                            "run_id": existing_query.run_id,
                            "timestamp": time.time()
                        }
                        yield before_function_message
                        
                        # Simulate function execution time
                        await asyncio.sleep(function_delay)
                        
                        # If this is provide_response_metadata, process it and send metadata message
                        if function_name == "provide_response_metadata":
                            try:
                                # Ensure resource is a dictionary
                                if not isinstance(resource, dict):
                                    logger.error(f"Resource is not a dictionary: {type(resource)}")
                                    continue
                                
                                # Extract arguments from the resource with proper type checking
                                arguments = resource.get("arguments")
                                if arguments is None:
                                    logger.error("No arguments found in resource")
                                    continue
                                
                                # If arguments is a string, try to parse it as JSON
                                if isinstance(arguments, str):
                                    try:
                                        function_args = json.loads(arguments)
                                    except json.JSONDecodeError as e:
                                        logger.error(f"Failed to parse arguments as JSON: {str(e)}")
                                        continue
                                elif isinstance(arguments, dict):
                                    function_args = arguments
                                else:
                                    logger.error(f"Arguments has unexpected type: {type(arguments)}")
                                    continue
                                
                                logger.info(f"Processing provide_response_metadata with args: {function_args}")
                                
                                # Process the metadata with proper type checking
                                metadata = function_args if isinstance(function_args, dict) else {}
                                
                                # Extract and process reference URLs with type checking
                                reference_urls = metadata.get("reference_urls", [])
                                if not isinstance(reference_urls, list):
                                    logger.warning(f"reference_urls is not a list: {type(reference_urls)}")
                                    reference_urls = []
                                
                                # Convert old format to new format if needed
                                processed_urls = []
                                for ref in reference_urls:
                                    if isinstance(ref, str):
                                        # Old format - just URL strings
                                        processed_urls.append({
                                            "url": ref,
                                            "title": "Reference",
                                            "snippet": ""
                                        })
                                    elif isinstance(ref, dict):
                                        # New format - objects with url, title, snippet
                                        processed_url = {
                                            "url": ref.get("url", "") if isinstance(ref.get("url"), str) else "",
                                            "title": ref.get("title", "Reference") if isinstance(ref.get("title"), str) else "Reference",
                                            "snippet": ref.get("snippet", "") if isinstance(ref.get("snippet"), str) else ""
                                        }
                                        processed_urls.append(processed_url)
                                    else:
                                        logger.warning(f"Skipping reference with unexpected type: {type(ref)}")
                                
                                # Extract follow-up questions with type checking
                                follow_up_questions = metadata.get("follow_up_questions", [])
                                if not isinstance(follow_up_questions, list):
                                    logger.warning(f"follow_up_questions is not a list: {type(follow_up_questions)}")
                                    follow_up_questions = []
                                
                                # Create and send the metadata message
                                metadata_message = {
                                    "type": "metadata",
                                    "reference_urls": processed_urls,
                                    "follow_up_questions": follow_up_questions,
                                    "thread_id": thread_id,
                                    "run_id": existing_query.run_id,
                                    "timestamp": time.time()
                                }
                                yield metadata_message
                                logger.info(f"Sent metadata message with {len(processed_urls)} references and {len(follow_up_questions)} follow-up questions")
                            except Exception as e:
                                logger.error(f"Error processing provide_response_metadata: {str(e)}")
                                logger.error(f"Exception details: {traceback.format_exc()}")
                                # No fallback for metadata, just log the error
                        
                        # If this is query_ticker_concepts, actually call the function instead of using stored output
                        elif function_name == "query_ticker_concepts":
                            try:
                                # Extract arguments from the resource with proper type checking
                                arguments = resource.get("arguments")
                                if arguments is None:
                                    logger.warning(f"No arguments found for query_ticker_concepts")
                                    continue
                                
                                # If arguments is a string, try to parse it as JSON
                                if isinstance(arguments, str):
                                    try:
                                        function_args = json.loads(arguments)
                                    except json.JSONDecodeError as e:
                                        logger.error(f"Failed to parse arguments as JSON: {str(e)}")
                                        continue
                                elif isinstance(arguments, dict):
                                    function_args = arguments
                                else:
                                    logger.error(f"Arguments has unexpected type: {type(arguments)}")
                                    continue
                                logger.info(f"Executing query_ticker_concepts with args: {function_args}")
                                
                                # Call the function with the extracted arguments
                                result = query_ticker_concepts(**function_args)
                                
                                # Check if the result is a coroutine (from an async function)
                                if asyncio.iscoroutine(result):
                                    logger.debug(f"Function {function_name} returned a coroutine, awaiting it")
                                    # Await the coroutine to get the actual result
                                    result = await result
                                
                                # Send the company_facts message with the actual result
                                company_facts_message = {
                                    "type": "company_facts",
                                    "data": result,
                                    "thread_id": thread_id,
                                    "run_id": existing_query.run_id,
                                    "timestamp": time.time()
                                }
                                yield company_facts_message
                                logger.info(f"Sent company_facts message with live data for ticker {function_args.get('ticker', 'unknown')}")
                            except Exception as e:
                                logger.error(f"Error executing query_ticker_concepts: {str(e)}")
                                # Fallback to stored output if available
                                if "output" in resource:
                                    logger.info(f"Falling back to stored output for query_ticker_concepts")
                                    company_facts_message = {
                                        "type": "company_facts",
                                        "data": resource.get("output", {}),
                                        "thread_id": thread_id,
                                        "run_id": existing_query.run_id,
                                        "timestamp": time.time()
                                    }
                                    yield company_facts_message
                                else:
                                    logger.error(f"No fallback output available for query_ticker_concepts")
                        
                        # Send progress message after function call
                        after_function_message = {
                            "type": "progress",
                            "content": "Processing results...",
                            "thread_id": thread_id,
                            "run_id": existing_query.run_id,
                            "timestamp": time.time()
                        }
                        yield after_function_message
                        
                        # Add a small delay between function calls
                        await asyncio.sleep(function_delay)
                    
                    # After function calls, send a message indicating we're generating the answer
                    generating_message = {
                        "type": "progress",
                        "content": "Generating your answer...",
                        "thread_id": thread_id,
                        "run_id": existing_query.run_id,
                        "timestamp": time.time()
                    }
                    yield generating_message
                    
                    # Calculate elapsed time and ensure we wait at least 8 seconds before sending the answer
                    elapsed_time = time.time() - start_time
                    min_response_time = 8.0  # Minimum time in seconds before sending the answer
                    
                    if elapsed_time < min_response_time:
                        wait_time = min_response_time - elapsed_time
                        logger.info(f"Waiting {wait_time:.2f} seconds to ensure minimum response time of {min_response_time} seconds")
                        await asyncio.sleep(wait_time)
                        elapsed_time = time.time() - start_time
                    
                    # Calculate remaining time and adjust streaming parameters
                    remaining_time = max_total_time - elapsed_time - 0.5  # 0.5s buffer for done message
                    
                    # Split the answer into smaller chunks to simulate streaming
                    answer = existing_query.llm_answer
                    
                    # Adjust chunk size and delay based on remaining time
                    total_chars = len(answer)
                    if total_chars > 0:
                        # Calculate how many chunks we need and the delay between them
                        chunk_size = max(10, min(50, total_chars // 20))  # Between 10 and 50 chars per chunk
                        num_chunks = (total_chars + chunk_size - 1) // chunk_size  # Ceiling division
                        
                        if num_chunks > 0:
                            chunk_delay = remaining_time / num_chunks
                            # Cap the delay to avoid too slow streaming
                            chunk_delay = min(0.1, chunk_delay)
                        else:
                            chunk_delay = 0.05
                    else:
                        chunk_size = 20
                        chunk_delay = 0.05
                    
                    # Split the answer into tokens (words and whitespace)
                    import re
                    tokens = re.findall(r'\S+|\s+', answer)
                    num_tokens = len(tokens)
                    
                    if num_tokens > 0:
                        # Calculate delay between tokens based on remaining time
                        token_delay = remaining_time / num_tokens
                        # Cap the delay to avoid too slow streaming
                        token_delay = min(0.1, token_delay)
                    else:
                        token_delay = 0.05
                    
                    # Stream the answer word by word
                    for token in tokens:
                        yield {
                            "content": token,
                            "thread_id": thread_id,
                            "run_id": existing_query.run_id,
                            "timestamp": time.time()
                        }
                        # Add a small delay between words
                        await asyncio.sleep(token_delay)
                    
                    # Send a done message
                    done_message = {
                        "content": "[DONE]",
                        "thread_id": thread_id,
                        "run_id": existing_query.run_id,
                        "timestamp": time.time()
                    }
                    yield done_message
                    
                    # Update the user_query record status to "completed" if auth0_sub was provided
                    if auth0_sub:
                        try:
                            async with async_session_context('user_data') as update_session:
                                # Import the UserQuery model
                                from models.user_query import UserQuery
                                
                                # Find the user_query record
                                from sqlalchemy import select
                                result = await update_session.execute(
                                    select(UserQuery).where(
                                        UserQuery.uid == auth0_sub,
                                        UserQuery.query_id == existing_query.id,
                                        UserQuery.status == "pending"
                                    )
                                )
                                user_query = result.scalars().first()
                                
                                if user_query:
                                    # Update the status and completed_time
                                    user_query.status = "completed"
                                    user_query.completed_time = datetime.datetime.now()
                                    await update_session.commit()
                                    
                                    logger.info(f"Updated user_query record status to completed for user {auth0_sub} and query {existing_query.id}")
                        except Exception as e:
                            logger.error(f"Error updating user_query record: {str(e)}")
                    
                    # Log total time taken
                    total_time = time.time() - start_time
                    logger.info(f"Cached response replay completed in {total_time:.2f} seconds")
                
                async for item in cached_response_generator():
                    yield item
                return
            
            # If no cached result, proceed with the normal API call
            logger.info(f"No cached result found, making new API request for thread {thread_id}")
            
            '''query_enhanced_progress = {
                "content": "Analysing your questions ...",
                "thread_id": thread_id,
                "run_id": None,
                "timestamp": time.time(),
                "type": "progress"
            }
            yield f"data: {json.dumps(query_enhanced_progress)}\n\n"
            '''
            
            # Enhance the query with context based on its classification
            enhanced_query, classification, additional_context = await enhance_query_with_context(user_query)
            logger.info(f"Enhanced query for streaming: {enhanced_query}...")
            
            # Set default cacheable value
            cacheable = True
            
            # 1. First check: if query is not finance-related AND no context required, yield message directly
            if "finance" in classification and classification["finance"] is False and "context" in classification and classification["context"] is False:
                logger.info(f"Non-financial query detected, returning message to client and no context detected")
                
                # Create a generator that yields the message
                async def non_finance_generator():
                    # Send a message indicating this is not a financial query
                    non_finance_message = {
                        "content": "As GoAI, a specialized financial analyst AI, I can only provide insights and answers related to finance, investments, and economics. Please keep your questions relevant to these areas.",
                        "thread_id": thread_id,
                        "run_id": None,
                        "timestamp": time.time()
                    }
                    yield non_finance_message
                    
                    # Send a done message
                    done_message = {
                        "content": "[DONE]",
                        "thread_id": thread_id,
                        "run_id": None,
                        "timestamp": time.time()
                    }
                    yield done_message
                
                async for item in non_finance_generator():
                    yield item
                return
            
            # 2. Second check: if context is required, redo classification with previous message + current query
            if "context" in classification and classification["context"] is True:
                # Set cacheable to false for context-aware queries
                cacheable = False
                
                async with async_session_context('user_data') as session:                    
                    # If we have auth0_sub, get previous messages for this user
                    if auth0_sub:
                        try:
                            # Instead of using user_query_service.get_user_queries which is heavy,
                            # directly query the database to get previous queries for this user
                            from sqlalchemy import select, desc
                            from models.user_query import UserQuery
                            from models.query import Query
                            
                            # Query to get previous user queries with their associated Query records
                            # Order by query_time desc (newest first)
                            query = (
                                select(UserQuery, Query)
                                .join(Query, UserQuery.query_id == Query.id)
                                .where(UserQuery.uid == auth0_sub)
                                .order_by(desc(UserQuery.query_time))
                                .limit(10)  # Limit to 10 previous queries
                            )
                            
                            result = await session.execute(query)
                            rows = result.all()
                            
                            # Initialize a list to store relevant previous queries
                            relevant_queries = []
                            
                            # Iterate through queries in descending order (newest to oldest)
                            for user_query_record, query_record in rows:
                                # Add this query to our list of relevant queries
                                relevant_queries.append(query_record.user_query)
                                
                                # If this query is cacheable, stop looking for more
                                # Default to True if cacheable is not present
                                if query_record.cacheable:
                                    logger.info(f"Found cacheable query, stopping search for previous queries")
                                    break
                            
                            # If we found any relevant queries
                            if relevant_queries:
                                # Reverse the list to get ascending order (oldest first)
                                relevant_queries.reverse()
                                
                                # Build the combined query string with all relevant previous queries
                                # Format: "Previous message 1: [query1]\nPrevious message 2: [query2]\n...\nCurrent message: [current_query]"
                                previous_messages_text = ""
                                for i, query_text in enumerate(relevant_queries):
                                    previous_messages_text += f"Previous message {i+1}: {query_text}\n"
                                
                                combined_query = f"{previous_messages_text}Current message: {user_query}"
                                logger.info(f"combined_query with multiple previous messages: {combined_query}")
                                
                                # Redo the classification with the combined query
                                new_enhanced_query, new_classification, additional_context = await enhance_query_with_context(combined_query)
                                
                                # Update the classification
                                classification = new_classification
                                logger.info(f"Reclassified query with context: {classification}")
                        except Exception as e:
                            logger.error(f"Error getting previous messages for context: {str(e)}")
                            logger.error(f"Exception details: {traceback.format_exc()}")
                            # Continue with the original classification if there's an error
            # 3. Third check: if query is not finance-related (after potential reclassification), yield message directly
            if "finance" in classification and classification["finance"] is False:
                logger.info(f"Non-financial query detected, returning message to client")
                
                # Create a generator that yields the message
                async def non_finance_generator():
                    # Send a message indicating this is not a financial query
                    non_finance_message = {
                        "content": "As GoAI, a specialized financial analyst AI, I can only provide insights and answers related to finance, investments, and economics. Please keep your questions relevant to these areas.",
                        "thread_id": thread_id,
                        "run_id": None,
                        "timestamp": time.time()
                    }
                    yield non_finance_message
                    
                    # Send a done message
                    done_message = {
                        "content": "[DONE]",
                        "thread_id": thread_id,
                        "run_id": None,
                        "timestamp": time.time()
                    }
                    yield done_message
                
                async for item in non_finance_generator():
                    yield item
                return

            # 4. Finally, if it's finance-related, proceed to create query and user_query record
            classification['origin_query'] = user_query
            async with async_session_context('user_data') as session:
                # Create a new record
                await session.execute(
                    text("""
                        INSERT INTO query (thread_id, user_query, status, model, system_prompt, type, cacheable)
                        VALUES (:thread_id, :user_query, 'pending', :model, :system_prompt, :type, :cacheable)
                    """),
                    {
                        "thread_id": thread_id,
                        "user_query": user_query,
                        "model": settings.OPENAI_ASSISTANT_MODEL,
                        "system_prompt": additional_context,
                        "type": classification["type"],
                        "cacheable": cacheable
                    }
                )
                await session.commit()
                logger.info(f"Created initial query record for thread {thread_id} without run_id")
                
                # If auth0_sub is provided, create a user_query record
                if auth0_sub:
                    try:
                        # Get the query ID
                        result = await session.execute(
                            text("SELECT id FROM query WHERE thread_id = :thread_id AND run_id IS NULL AND user_query = :user_query ORDER BY created_at DESC LIMIT 1"),
                            {"thread_id": thread_id, "user_query": user_query}
                        )
                        query_record = result.fetchone()
                        
                        if query_record:
                            # Import the UserQuery model
                            from models.user_query import UserQuery
                            
                            # Create a new user_query record
                            user_query_record = UserQuery(
                                uid=auth0_sub,
                                query_id=query_record[0],
                                query_time=datetime.datetime.now(),
                                status="pending"
                            )
                            
                            # Add and commit the record
                            session.add(user_query_record)
                            await session.commit()
                            
                            logger.info(f"Created user_query record for user {auth0_sub} and query {query_record[0]}")

                            # Yield user_query_confirmed message immediately after successful creation
                            user_query_confirmed_message = {
                                "type": "user_query_confirmed",
                                "value": user_query_record.id,
                                "thread_id": thread_id,
                                "timestamp": time.time()
                            }
                            yield user_query_confirmed_message
                    except Exception as e:
                        logger.error(f"Error creating user_query record: {str(e)}")
                        logger.error(f"Exception details: {traceback.format_exc()}")

            '''
            query_enhanced_done_progress = {
                "content": "Optimised your questions ...",
                "thread_id": thread_id,
                "run_id": None,
                "timestamp": time.time(),
                "type": "progress"
            }
            yield f"data: {json.dumps(query_enhanced_done_progress)}\n\n"
            '''

            # Format the message for the provider
            messages = [{"role": "user", "content": enhanced_query}]
            
            # Call the provider with stream=True to get a generator
            response_generator = await self.provider.chat_completion(
                messages=messages,
                model=model,
                thread_id=thread_id,
                stream=True,
                classification=classification,
                auth0_sub=auth0_sub  # Pass the auth0_sub to the provider
            )
            
            # Return the generator directly
            # Schedule post-response pruning as a background task
            asyncio.create_task(self.pruning_manager.post_response_prune(thread_id))
            
            async for item in response_generator:
                yield item
            return
            
        except Exception as e:
            logger.error(f"Error in async assistant streaming service: {str(e)}")
            # Create a generator that yields an error
            async def error_generator():
                yield {
                    "error": str(e),
                    "content": f"Error: {str(e)}"
                }
            async for item in error_generator():
                yield item
            return
    
    async def query(self, 
                    user_query: str, 
                    thread_id: Optional[str] = None, 
                    model: str = "gpt-4-turbo-preview",
                    auth0_sub: Optional[str] = None) -> Dict[str, Any]:
        """
        Process a user query using the OpenAI Assistant API asynchronously.
        
        Args:
            user_query: The user's query text
            thread_id: Optional thread ID for continuing a conversation
            model: The model to use (default: gpt-4-turbo-preview)
            auth0_sub: Optional user identifier for thread persistence
            
        Returns:
            Dict containing the response and metadata
        """
        try:
            # Get or create thread ID
            thread_id = await self._get_or_create_thread(thread_id, auth0_sub)
            
            # Enhance the query with context based on its classification
            enhanced_query = await enhance_query_with_context(user_query)
            logger.info(f"Enhanced query: {enhanced_query[:50]}...")
            
            # Format the message for the provider
            messages = [{"role": "user", "content": enhanced_query}]
            
            # Call the provider - this returns an async generator
            response_generator = await self.provider.chat_completion(
                messages=messages,
                model=model,
                thread_id=thread_id
            )
            
            # Process the async generator to collect the response
            full_content = ""
            last_response = None
            current_thread_id = thread_id
            current_run_id = None
            
            # Iterate through the generator to collect all chunks
            async for chunk in response_generator:
                # Keep track of the last response chunk
                last_response = chunk
                
                # Update thread_id and run_id if available
                if "thread_id" in chunk and chunk["thread_id"]:
                    current_thread_id = chunk["thread_id"]
                if "run_id" in chunk and chunk["run_id"]:
                    current_run_id = chunk["run_id"]
                
                # Extract and accumulate content from each chunk
                if "choices" in chunk and chunk["choices"] and "delta" in chunk["choices"][0]:
                    delta = chunk["choices"][0]["delta"]
                    if "content" in delta and delta["content"]:
                        full_content += delta["content"]
            
            # If we didn't get any content but have a last response with an error
            if not full_content and last_response and "error" in last_response:
                raise Exception(last_response["error"])
            
            # If a new thread was created (different from the one we started with) and auth0_sub is provided, store in Redis
            if auth0_sub and current_thread_id and current_thread_id != thread_id:
                redis_client = await get_async_redis_client()
                await redis_client.set(f"thread:{auth0_sub}", current_thread_id)
                logger.info(f"Updated thread ID {current_thread_id} for user {auth0_sub} in Redis")
            
            # Schedule post-response pruning as a background task
            asyncio.create_task(self.pruning_manager.post_response_prune(current_thread_id))
            
            # Return the formatted response
            return {
                "response": full_content,
                "thread_id": current_thread_id,
                "run_id": current_run_id,
                "model": model
            }
            
        except Exception as e:
            logger.error(f"Error in async assistant service: {str(e)}")
            return {
                "response": f"Error: {str(e)}",
                "error": True
            }
    
    async def create_thread(self) -> Dict[str, Any]:
        """
        Create a new conversation thread asynchronously.
        
        Returns:
            Dict containing the thread ID
        """
        try:
            # Ensure the assistant is initialized
            await self.provider._ensure_assistant_initialized()
            
            thread = await self.client.beta.threads.create()
            return {
                "thread_id": thread.id,
                "created": True
            }
        except Exception as e:
            logger.error(f"Error creating thread: {str(e)}")
            return {
                "error": str(e),
                "created": False
            }
    
    async def delete_thread(self, thread_id: str) -> Dict[str, Any]:
        """
        Delete a conversation thread asynchronously.
        
        Args:
            thread_id: The thread ID to delete
            
        Returns:
            Dict containing the result
        """
        try:
            await self.client.beta.threads.delete(thread_id)
            return {
                "deleted": True,
                "thread_id": thread_id
            }
        except Exception as e:
            logger.error(f"Error deleting thread {thread_id}: {str(e)}")
            return {
                "error": str(e),
                "deleted": False,
                "thread_id": thread_id
            }
    
    async def get_thread_messages(self, thread_id: str) -> Dict[str, Any]:
        """
        Get all messages in a thread asynchronously.
        
        Args:
            thread_id: The thread ID
            
        Returns:
            Dict containing the messages
        """
        try:
            messages = await self.client.beta.threads.messages.list(thread_id=thread_id)
            
            # Format messages for the response
            formatted_messages = []
            for msg in messages.data:
                content = msg.content[0].text.value if msg.content else ""
                formatted_messages.append({
                    "id": msg.id,
                    "role": msg.role,
                    "content": content,
                    "created_at": msg.created_at
                })
            
            return {
                "messages": formatted_messages,
                "thread_id": thread_id
            }
        except Exception as e:
            logger.error(f"Error getting messages for thread {thread_id}: {str(e)}")
            return {
                "error": str(e),
                "thread_id": thread_id
            }
            
    async def cancel_run(self, thread_id: str, run_id: str) -> Dict[str, Any]:
        """
        Cancel an in-progress run asynchronously.
        
        Args:
            thread_id: The thread ID
            run_id: The run ID to cancel
            
        Returns:
            Dict containing the result
        """
        try:
            result = await self.client.beta.threads.runs.cancel(
                thread_id=thread_id,
                run_id=run_id
            )
            return {
                "cancelled": True,
                "thread_id": thread_id,
                "run_id": run_id,
                "status": result.status
            }
        except Exception as e:
            logger.error(f"Error cancelling run {run_id} in thread {thread_id}: {str(e)}")
            return {
                "error": str(e),
                "cancelled": False,
                "thread_id": thread_id,
                "run_id": run_id
            }
            
    async def cancel_active_run(self, auth0_sub: Optional[str] = None) -> Dict[str, Any]:
        """
        Cancel the most recent active run for the user asynchronously.
        
        Args:
            auth0_sub: User identifier for thread retrieval
            
        Returns:
            Dict containing the result
        """
        try:
            logger.info(f"Starting async cancel_active_run for user {auth0_sub}")
            
            # Get thread_id from Redis using auth0_sub
            if not auth0_sub:
                logger.warning("No auth0_sub provided for async cancel_active_run")
                return {
                    "error": "User identifier (auth0_sub) is required",
                    "cancelled": False
                }
                
            redis_client = await get_async_redis_client()
            thread_id = await redis_client.get(f"thread:{auth0_sub}")
            
            if not thread_id:
                logger.warning(f"No thread found in Redis for user {auth0_sub}")
                return {
                    "error": "No active thread found for this user",
                    "cancelled": False
                }
                
            # No need to decode as redis_client is configured with decode_responses=True
            logger.info(f"Found thread_id {thread_id} for user {auth0_sub}")
            
            # List all runs for this thread
            runs = await self.client.beta.threads.runs.list(thread_id=thread_id)
            logger.info(f"Found {len(runs.data)} runs for thread {thread_id}")
            
            # Find the most recent active run (in "queued", "in_progress", or "requires_action" status)
            active_run = None
            for run in runs.data:
                logger.debug(f"Examining run {run.id} with status {run.status}")
                if run.status in ["queued", "in_progress", "requires_action"]:
                    if active_run is None or run.created_at > active_run.created_at:
                        active_run = run
                        logger.debug(f"Found newer active run: {run.id} with status {run.status}")
            
            if not active_run:
                # Double-check by directly retrieving the most recent run
                if runs.data:
                    most_recent_run = runs.data[0]  # Assuming runs are sorted by recency
                    # Explicitly check the status of the most recent run
                    run_details = await self.client.beta.threads.runs.retrieve(
                        thread_id=thread_id,
                        run_id=most_recent_run.id
                    )
                    logger.info(f"Double-checking most recent run {most_recent_run.id}, status: {run_details.status}")
                    
                    if run_details.status in ["queued", "in_progress", "requires_action"]:
                        active_run = run_details
                        logger.info(f"Found active run {active_run.id} with status {active_run.status} through direct check")
                
                if not active_run:
                    logger.warning(f"No active runs found for thread {thread_id}")
                    return {
                        "error": "No active runs found for this thread",
                        "cancelled": False,
                        "thread_id": thread_id
                    }
            
            logger.info(f"Selected active run {active_run.id} with status {active_run.status} for cancellation")
            
            # Handle requires_action state differently - cannot directly cancel
            if active_run.status == "requires_action" and hasattr(active_run, 'required_action') and hasattr(active_run.required_action, 'submit_tool_outputs'):
                logger.info(f"Run {active_run.id} is in requires_action state, attempting to submit error outputs")
                # Get required actions
                required_actions = active_run.required_action.submit_tool_outputs.tool_calls
                logger.info(f"Run requires {len(required_actions)} tool outputs")
                
                # Submit error responses for each required action
                import json
                error_outputs = [
                    {"tool_call_id": action.id, "output": json.dumps({"error": "Operation cancelled by user"})}
                    for action in required_actions
                ]
                
                # Submit the error outputs to fail the run
                logger.info(f"Submitting error outputs for run {active_run.id}")
                try:
                    result = await self.client.beta.threads.runs.submit_tool_outputs(
                        thread_id=thread_id,
                        run_id=active_run.id,
                        tool_outputs=error_outputs
                    )
                    logger.info(f"Successfully submitted error outputs, run status: {result.status}")
                    
                    # Wait for the run to reach a terminal state
                    max_wait_time = 5  # seconds
                    wait_interval = 0.5  # seconds
                    total_waited = 0
                    
                    while total_waited < max_wait_time:
                        # Check the current status
                        current_status = await self.client.beta.threads.runs.retrieve(
                            thread_id=thread_id,
                            run_id=active_run.id
                        )
                        
                        if current_status.status in ["completed", "failed", "cancelled", "incomplete", "expired"]:
                            logger.info(f"Run {active_run.id} reached terminal state: {current_status.status}")
                            break
                        
                        logger.info(f"Waiting for run {active_run.id} to complete, current status: {current_status.status}")
                        await asyncio.sleep(wait_interval)
                        total_waited += wait_interval
                    
                    # Return success
                    return {
                        "cancelled": True,
                        "thread_id": thread_id,
                        "run_id": active_run.id,
                        "status": current_status.status if 'current_status' in locals() else result.status,
                        "original_status": "requires_action",
                        "cancellation_method": "error_submission"
                    }
                except Exception as tool_error:
                    # If submission fails (possibly because run state changed), fall back to cancel API
                    logger.warning(f"Error submitting tool outputs: {str(tool_error)}, falling back to cancel API")
                    # Continue to the cancel code below instead of returning error
            
            # For other states (queued, in_progress), use the cancel endpoint
            logger.info(f"Cancelling run {active_run.id} in thread {thread_id}")
            result = await self.client.beta.threads.runs.cancel(
                thread_id=thread_id,
                run_id=active_run.id
            )
            
            # Wait for the run to reach a terminal state
            max_wait_time = 5  # seconds
            wait_interval = 0.5  # seconds
            total_waited = 0
            
            while total_waited < max_wait_time:
                # Check the current status
                current_status = await self.client.beta.threads.runs.retrieve(
                    thread_id=thread_id,
                    run_id=active_run.id
                )
                
                if current_status.status in ["cancelled", "failed", "completed", "incomplete", "expired"]:
                    logger.info(f"Run {active_run.id} reached terminal state: {current_status.status}")
                    break
                
                logger.info(f"Waiting for run {active_run.id} to be cancelled, current status: {current_status.status}")
                await asyncio.sleep(wait_interval)
                total_waited += wait_interval
            
            final_status = current_status.status if 'current_status' in locals() else result.status
            logger.info(f"Run {active_run.id} cancellation result: status={final_status}")
            
            return {
                "cancelled": final_status == "cancelled",
                "thread_id": thread_id,
                "run_id": active_run.id,
                "status": final_status,
                "original_status": active_run.status,
                "cancellation_method": "cancel_endpoint"
            }
            
        except Exception as e:
            logger.error(f"Error cancelling active run: {str(e)}", exc_info=True)
            return {
                "error": str(e),
                "cancelled": False
            }
