#!/usr/bin/env python3
"""
OpenAPI Specification Updater

This script validates and updates the existing OpenAPI specification by:
1. Validating YAML syntax
2. Checking for missing endpoints by scanning the codebase
3. Suggesting updates needed
4. Optionally auto-updating the specification
"""

import os
import sys
import re
import yaml
import ast
from pathlib import Path
from typing import Dict, List, Set, Tuple
from datetime import datetime

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class OpenAPIUpdater:
    def __init__(self, openapi_file: str = "gpt.openapi.yaml"):
        self.openapi_file = project_root / openapi_file
        self.spec = None
        self.discovered_endpoints = set()
        self.existing_endpoints = set()
        
    def load_existing_spec(self) -> bool:
        """Load and validate the existing OpenAPI specification."""
        try:
            with open(self.openapi_file, 'r', encoding='utf-8') as f:
                self.spec = yaml.safe_load(f)
            
            print(f"✅ Successfully loaded {self.openapi_file}")
            
            # Extract existing endpoints
            if "paths" in self.spec:
                for path, methods in self.spec["paths"].items():
                    for method in methods.keys():
                        self.existing_endpoints.add(f"{method.upper()} {path}")
                        
            print(f"📋 Found {len(self.existing_endpoints)} existing endpoints in spec")
            return True
            
        except yaml.YAMLError as e:
            print(f"❌ YAML syntax error in {self.openapi_file}: {e}")
            return False
        except FileNotFoundError:
            print(f"❌ OpenAPI file not found: {self.openapi_file}")
            return False
        except Exception as e:
            print(f"❌ Error loading OpenAPI spec: {e}")
            return False
    
    def scan_fastapi_routes(self, file_path: Path) -> Set[str]:
        """Scan a FastAPI route file for endpoint definitions."""
        endpoints = set()
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Look for router decorators
            patterns = [
                r'@router\.(get|post|put|patch|delete)\(["\']([^"\']+)["\']',
                r'@app\.(get|post|put|patch|delete)\(["\']([^"\']+)["\']'
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for method, path in matches:
                    endpoints.add(f"{method.upper()} {path}")
                    
        except Exception as e:
            print(f"⚠️  Error scanning {file_path}: {e}")
            
        return endpoints
    
    def scan_flask_routes(self, file_path: Path) -> Set[str]:
        """Scan a Flask route file for endpoint definitions."""
        endpoints = set()
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Look for Flask route decorators
            patterns = [
                r'@\w+\.route\(["\']([^"\']+)["\'].*?methods=\[([^\]]+)\]',
                r'@\w+\.route\(["\']([^"\']+)["\'].*?methods=\[\'([^\']+)\'\]',
                r'@\w+\.route\(["\']([^"\']+)["\'].*?methods=\["([^"]+)"\]'
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                for path, methods_str in matches:
                    methods = re.findall(r'["\']([^"\']+)["\']', methods_str)
                    for method in methods:
                        endpoints.add(f"{method.upper()} {path}")
            
            # Also look for simple route decorators (default GET)
            simple_pattern = r'@\w+\.route\(["\']([^"\']+)["\'][^)]*\)'
            simple_matches = re.findall(simple_pattern, content)
            for path in simple_matches:
                # Check if this path already has methods defined
                has_methods = any(f"GET {path}" in endpoints or 
                                f"POST {path}" in endpoints or
                                f"PUT {path}" in endpoints or
                                f"PATCH {path}" in endpoints or
                                f"DELETE {path}" in endpoints 
                                for _ in [None])
                if not has_methods:
                    endpoints.add(f"GET {path}")
                    
        except Exception as e:
            print(f"⚠️  Error scanning {file_path}: {e}")
            
        return endpoints
    
    def discover_all_endpoints(self) -> Set[str]:
        """Discover all API endpoints in the codebase."""
        all_endpoints = set()
        api_dir = project_root / "api"
        
        print("🔍 Scanning codebase for API endpoints...")
        
        # Scan FastAPI async route files
        for file_path in api_dir.glob("async_*.py"):
            endpoints = self.scan_fastapi_routes(file_path)
            all_endpoints.update(endpoints)
            if endpoints:
                print(f"  📁 {file_path.name}: {len(endpoints)} endpoints")
        
        # Scan Flask route files
        for file_path in api_dir.glob("*.py"):
            if not file_path.name.startswith("async_") and file_path.name != "__init__.py":
                endpoints = self.scan_flask_routes(file_path)
                all_endpoints.update(endpoints)
                if endpoints:
                    print(f"  📁 {file_path.name}: {len(endpoints)} endpoints")
        
        # Scan main app files
        for app_file in ["app.py", "async_app.py"]:
            app_path = project_root / app_file
            if app_path.exists():
                if "async" in app_file:
                    endpoints = self.scan_fastapi_routes(app_path)
                else:
                    endpoints = self.scan_flask_routes(app_path)
                all_endpoints.update(endpoints)
                if endpoints:
                    print(f"  📁 {app_file}: {len(endpoints)} endpoints")
        
        self.discovered_endpoints = all_endpoints
        print(f"📋 Total discovered endpoints: {len(all_endpoints)}")
        return all_endpoints
    
    def compare_endpoints(self) -> Tuple[Set[str], Set[str]]:
        """Compare discovered endpoints with existing spec."""
        missing_in_spec = self.discovered_endpoints - self.existing_endpoints
        extra_in_spec = self.existing_endpoints - self.discovered_endpoints
        
        return missing_in_spec, extra_in_spec
    
    def generate_report(self) -> str:
        """Generate a comparison report."""
        missing, extra = self.compare_endpoints()
        
        report = []
        report.append("=" * 60)
        report.append("OpenAPI Specification Analysis Report")
        report.append("=" * 60)
        report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        report.append(f"📊 Summary:")
        report.append(f"  • Endpoints in codebase: {len(self.discovered_endpoints)}")
        report.append(f"  • Endpoints in OpenAPI spec: {len(self.existing_endpoints)}")
        report.append(f"  • Missing from spec: {len(missing)}")
        report.append(f"  • Extra in spec: {len(extra)}")
        report.append("")
        
        if missing:
            report.append("❌ Missing from OpenAPI specification:")
            for endpoint in sorted(missing):
                report.append(f"  • {endpoint}")
            report.append("")
        
        if extra:
            report.append("⚠️  In OpenAPI spec but not found in codebase:")
            for endpoint in sorted(extra):
                report.append(f"  • {endpoint}")
            report.append("")
        
        if not missing and not extra:
            report.append("✅ OpenAPI specification is up to date!")
        else:
            report.append("💡 Recommendations:")
            if missing:
                report.append("  • Add missing endpoints to the OpenAPI specification")
            if extra:
                report.append("  • Review extra endpoints - they may be deprecated or incorrectly detected")
        
        report.append("")
        report.append("=" * 60)
        
        return "\n".join(report)
    
    def validate_and_report(self) -> bool:
        """Main validation and reporting function."""
        print("🚀 Starting OpenAPI specification analysis...")
        
        # Load existing specification
        if not self.load_existing_spec():
            return False
        
        # Discover endpoints in codebase
        self.discover_all_endpoints()
        
        # Generate and display report
        report = self.generate_report()
        print(report)
        
        # Save report to file
        report_file = project_root / "openapi_analysis_report.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"📄 Report saved to {report_file}")
        
        missing, extra = self.compare_endpoints()
        return len(missing) == 0 and len(extra) == 0

def main():
    """Main function."""
    updater = OpenAPIUpdater()
    
    if len(sys.argv) > 1 and sys.argv[1] == "--check":
        # Just validate and report
        is_up_to_date = updater.validate_and_report()
        sys.exit(0 if is_up_to_date else 1)
    else:
        # Default: validate and report
        updater.validate_and_report()

if __name__ == "__main__":
    main()
