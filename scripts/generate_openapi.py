#!/usr/bin/env python3
"""
OpenAPI Specification Generator

This script automatically generates/updates the OpenAPI specification by analyzing
the codebase for API routes and their definitions.
"""

import os
import sys
import re
import ast
import yaml
from pathlib import Path
from typing import Dict, List, Any, Optional, Set
from datetime import datetime

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class OpenAPIGenerator:
    def __init__(self):
        self.base_spec = {
            "openapi": "3.0.0",
            "info": {
                "title": "Go GPT Backend Async API",
                "description": "API documentation for Go GPT Backend's asynchronous endpoints",
                "version": "1.0.0"
            },
            "servers": [
                {
                    "url": "/",
                    "description": "Base API path"
                }
            ],
            "tags": [],
            "paths": {},
            "components": {
                "securitySchemes": {
                    "Auth0": {
                        "type": "apiKey",
                        "name": "auth0_sub",
                        "in": "header",
                        "description": "Auth0 subject identifier for authenticated requests"
                    }
                },
                "schemas": {}
            }
        }
        
        self.discovered_routes = []
        self.discovered_schemas = set()
        
    def scan_fastapi_routes(self, file_path: str) -> List[Dict]:
        """Scan FastAPI route files for endpoint definitions."""
        routes = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Parse the AST to find route decorators
            tree = ast.parse(content)
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    route_info = self._extract_route_info(node, content)
                    if route_info:
                        routes.append(route_info)
                        
        except Exception as e:
            print(f"Error scanning {file_path}: {e}")
            
        return routes
    
    def _extract_route_info(self, func_node: ast.FunctionDef, content: str) -> Optional[Dict]:
        """Extract route information from a function node."""
        route_info = {
            "function_name": func_node.name,
            "methods": [],
            "path": None,
            "tags": [],
            "security": [],
            "parameters": [],
            "description": "",
            "summary": ""
        }
        
        # Extract docstring
        if (func_node.body and 
            isinstance(func_node.body[0], ast.Expr) and 
            isinstance(func_node.body[0].value, ast.Constant)):
            docstring = func_node.body[0].value.value
            if isinstance(docstring, str):
                route_info["description"] = docstring.strip()
                # Use first line as summary
                lines = docstring.strip().split('\n')
                route_info["summary"] = lines[0].strip() if lines else ""
        
        # Look for decorators
        for decorator in func_node.decorator_list:
            if isinstance(decorator, ast.Call):
                if hasattr(decorator.func, 'attr'):
                    method = decorator.func.attr.lower()
                    if method in ['get', 'post', 'put', 'patch', 'delete']:
                        route_info["methods"].append(method.upper())
                        
                        # Extract path from decorator arguments
                        if decorator.args and isinstance(decorator.args[0], ast.Constant):
                            route_info["path"] = decorator.args[0].value
                            
                elif hasattr(decorator.func, 'id'):
                    # Handle middleware decorators
                    if 'auth' in decorator.func.id.lower():
                        route_info["security"].append("Auth0")
        
        # Extract function parameters for query/path parameters
        for arg in func_node.args.args:
            if arg.arg not in ['request', 'background_tasks', 'auth0_sub']:
                param_info = {
                    "name": arg.arg,
                    "in": "query",  # Default, could be refined
                    "required": False,
                    "schema": {"type": "string"}
                }
                route_info["parameters"].append(param_info)
        
        return route_info if route_info["path"] and route_info["methods"] else None
    
    def scan_all_routes(self) -> List[Dict]:
        """Scan all API route files."""
        api_dir = project_root / "api"
        routes = []
        
        # Scan async route files
        for file_path in api_dir.glob("async_*.py"):
            file_routes = self.scan_fastapi_routes(str(file_path))
            routes.extend(file_routes)
            
        return routes
    
    def generate_tags(self, routes: List[Dict]) -> List[Dict]:
        """Generate tags based on discovered routes."""
        tag_map = {
            "earnings": {"name": "Earnings Calendar", "description": "Endpoints for earnings calendar data"},
            "financial": {"name": "Financial Calendar", "description": "Endpoints for financial calendar data"},
            "sec": {"name": "SEC Filings", "description": "Endpoints for SEC filing data"},
            "user_query": {"name": "User Queries", "description": "Endpoints for user query management"},
            "shared": {"name": "Shared Queries", "description": "Endpoints for sharing queries"},
            "assistant": {"name": "Assistant", "description": "Endpoints for OpenAI Assistant integration"},
            "cache": {"name": "Cache", "description": "Endpoints for cache management"},
            "models": {"name": "Models", "description": "Endpoints for model information"},
            "root": {"name": "Root", "description": "Root endpoints"},
            "conversation": {"name": "Conversation", "description": "Endpoints for conversation management"}
        }
        
        used_tags = set()
        for route in routes:
            path = route.get("path", "")
            if "earnings" in path:
                used_tags.add("earnings")
            elif "events" in path or "financial" in path:
                used_tags.add("financial")
            elif "sec" in path:
                used_tags.add("sec")
            elif "user_query" in path or "ticker_facts" in path:
                used_tags.add("user_query")
            elif "share" in path:
                used_tags.add("shared")
            elif "assistant" in path:
                used_tags.add("assistant")
            elif "cache" in path:
                used_tags.add("cache")
            elif "models" in path:
                used_tags.add("models")
            elif path == "/" or "healthcheck" in path:
                used_tags.add("root")
            else:
                used_tags.add("conversation")
        
        return [tag_map[tag] for tag in used_tags if tag in tag_map]
    
    def generate_basic_schemas(self) -> Dict[str, Any]:
        """Generate basic schema definitions."""
        return {
            "ErrorResponse": {
                "type": "object",
                "properties": {
                    "detail": {
                        "type": "string",
                        "description": "Error message"
                    }
                }
            },
            "Pagination": {
                "type": "object",
                "properties": {
                    "page": {"type": "integer", "description": "Current page number"},
                    "page_size": {"type": "integer", "description": "Number of items per page"},
                    "total_pages": {"type": "integer", "description": "Total number of pages"},
                    "total_items": {"type": "integer", "description": "Total number of items"}
                }
            }
        }
    
    def convert_routes_to_paths(self, routes: List[Dict]) -> Dict[str, Any]:
        """Convert discovered routes to OpenAPI paths."""
        paths = {}

        for route in routes:
            path = route["path"]
            method = route["methods"][0].lower() if route["methods"] else "get"

            if path not in paths:
                paths[path] = {}

            # Determine tags
            tags = []
            if "earnings" in path:
                tags = ["Earnings Calendar"]
            elif "events" in path:
                tags = ["Financial Calendar"]
            elif "sec" in path:
                tags = ["SEC Filings"]
            elif "user_query" in path or "ticker_facts" in path:
                tags = ["User Queries"]
            elif "share" in path:
                tags = ["Shared Queries"]
            elif "assistant" in path:
                tags = ["Assistant"]
            elif "cache" in path:
                tags = ["Cache"]
            elif "models" in path:
                tags = ["Models"]
            elif path == "/" or "healthcheck" in path:
                tags = ["Root"]
            else:
                tags = ["Conversation"]

            # Build operation
            operation = {
                "summary": route["summary"] or f"{method.upper()} {path}",
                "description": route["description"] or f"Handle {method.upper()} request for {path}",
                "tags": tags,
                "responses": {
                    "200": {
                        "description": "Successful response",
                        "content": {
                            "application/json": {
                                "schema": {"type": "object"}
                            }
                        }
                    },
                    "500": {
                        "description": "Server error",
                        "content": {
                            "application/json": {
                                "schema": {"$ref": "#/components/schemas/ErrorResponse"}
                            }
                        }
                    }
                }
            }

            # Add security if needed
            if route["security"]:
                operation["security"] = [{"Auth0": []}]
                operation["responses"]["401"] = {
                    "description": "Authentication required",
                    "content": {
                        "application/json": {
                            "schema": {"$ref": "#/components/schemas/ErrorResponse"}
                        }
                    }
                }

            # Add parameters
            if route["parameters"]:
                operation["parameters"] = []
                for param in route["parameters"]:
                    operation["parameters"].append({
                        "name": param["name"],
                        "in": param["in"],
                        "required": param["required"],
                        "schema": param["schema"]
                    })

            paths[path][method] = operation

        return paths

    def generate_openapi_spec(self) -> Dict[str, Any]:
        """Generate the complete OpenAPI specification."""
        print("🔍 Scanning codebase for API routes...")
        routes = self.scan_all_routes()
        print(f"📋 Found {len(routes)} route definitions")

        # Generate tags
        self.base_spec["tags"] = self.generate_tags(routes)

        # Generate basic schemas
        self.base_spec["components"]["schemas"] = self.generate_basic_schemas()

        # Convert routes to OpenAPI paths
        self.base_spec["paths"] = self.convert_routes_to_paths(routes)

        return self.base_spec
    
    def merge_with_existing(self, new_spec: Dict[str, Any], existing_file: str) -> Dict[str, Any]:
        """Merge new specification with existing one."""
        if os.path.exists(existing_file):
            try:
                with open(existing_file, 'r', encoding='utf-8') as f:
                    existing_spec = yaml.safe_load(f)
                
                # Preserve existing paths and schemas, add new ones
                if "paths" in existing_spec:
                    new_spec["paths"] = existing_spec["paths"]
                if "components" in existing_spec and "schemas" in existing_spec["components"]:
                    new_spec["components"]["schemas"].update(existing_spec["components"]["schemas"])
                    
                print("✅ Merged with existing OpenAPI specification")
                return new_spec
                
            except Exception as e:
                print(f"⚠️  Error reading existing spec: {e}")
                
        return new_spec
    
    def save_spec(self, spec: Dict[str, Any], output_file: str):
        """Save the OpenAPI specification to a YAML file."""
        try:
            # Add generation timestamp as comment
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(f"# Generated on {timestamp}\n")
                f.write(f"# Auto-generated OpenAPI specification\n\n")
                yaml.dump(spec, f, default_flow_style=False, sort_keys=False, indent=2)
                
            print(f"✅ OpenAPI specification saved to {output_file}")
            
        except Exception as e:
            print(f"❌ Error saving specification: {e}")
            sys.exit(1)

def main():
    """Main function to generate OpenAPI specification."""
    print("🚀 Starting OpenAPI specification generation...")
    
    generator = OpenAPIGenerator()
    
    # Generate new specification
    spec = generator.generate_openapi_spec()
    
    # Merge with existing specification
    output_file = str(project_root / "gpt.openapi.yaml")
    merged_spec = generator.merge_with_existing(spec, output_file)
    
    # Save the specification
    generator.save_spec(merged_spec, output_file)
    
    print("🎉 OpenAPI specification generation completed!")

if __name__ == "__main__":
    main()
